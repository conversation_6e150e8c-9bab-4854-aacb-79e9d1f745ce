#!/usr/bin/env python3
"""
CosyVoice 文本预处理模块
处理中文分词、标点处理、数字转换、多语言支持等
"""

import re
import logging
from typing import List, Dict, Tuple, Optional
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TextProcessor:
    """文本预处理器"""
    
    def __init__(self):
        """初始化文本处理器"""
        self.chinese_punctuation = "，。！？；：""''（）【】《》、"
        self.english_punctuation = ",.!?;:\"'()[]{}<>"
        
        # 语言标记
        self.language_tags = {
            'zh': '<|zh|>',
            'en': '<|en|>',
            'jp': '<|jp|>',
            'yue': '<|yue|>',
            'ko': '<|ko|>'
        }
        
        # 情感标记
        self.emotion_tags = {
            'strong': ('<strong>', '</strong>'),
            'laughter': ('<laughter>', '</laughter>'),
            'breath': '[breath]',
            'laugh': '[laughter]'
        }
        
        # 数字转换映射
        self.digit_map = {
            '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
            '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
        
        # 单位转换
        self.unit_map = {
            '十': '十', '百': '百', '千': '千', '万': '万',
            '亿': '亿', '兆': '兆'
        }
    
    def detect_language(self, text: str) -> str:
        """
        检测文本主要语言
        
        Args:
            text: 输入文本
            
        Returns:
            语言代码 (zh/en/jp/ko)
        """
        # 移除标点符号
        clean_text = re.sub(r'[^\w\s]', '', text)
        
        # 统计各语言字符数量
        chinese_count = len(re.findall(r'[\u4e00-\u9fff]', clean_text))
        english_count = len(re.findall(r'[a-zA-Z]', clean_text))
        japanese_count = len(re.findall(r'[\u3040-\u309f\u30a0-\u30ff]', clean_text))
        korean_count = len(re.findall(r'[\uac00-\ud7af]', clean_text))
        
        # 确定主要语言
        counts = {
            'zh': chinese_count,
            'en': english_count,
            'jp': japanese_count,
            'ko': korean_count
        }
        
        main_language = max(counts, key=counts.get)
        
        # 如果都很少，默认为中文
        if counts[main_language] < 3:
            main_language = 'zh'
        
        logger.debug(f"检测到主要语言: {main_language}, 字符统计: {counts}")
        return main_language
    
    def normalize_punctuation(self, text: str, target_lang: str = 'zh') -> str:
        """
        标点符号规范化
        
        Args:
            text: 输入文本
            target_lang: 目标语言
            
        Returns:
            规范化后的文本
        """
        if target_lang == 'zh':
            # 英文标点转中文标点
            replacements = {
                ',': '，', '.': '。', '!': '！', '?': '？',
                ';': '；', ':': '：',
                '(': '（', ')': '）', '[': '【', ']': '】',
                '<': '《', '>': '》'
            }
        else:
            # 中文标点转英文标点
            replacements = {
                '，': ',', '。': '.', '！': '!', '？': '?',
                '；': ';', '：': ':',
                '（': '(', '）': ')', '【': '[', '】': ']',
                '《': '<', '》': '>'
            }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def convert_numbers_to_chinese(self, text: str) -> str:
        """
        将阿拉伯数字转换为中文数字

        Args:
            text: 输入文本

        Returns:
            转换后的文本
        """
        # 先处理特殊情况，避免被通用规则覆盖

        # 1. 处理年份（4位数字+年）- 按位读出
        def convert_year(match):
            year = match.group(1)
            return ''.join(self.digit_map[d] for d in year) + '年'

        text = re.sub(r'(\d{4})年', convert_year, text)

        # 2. 处理时间（时:分格式）- 标准时间读法
        def convert_time(match):
            hour = match.group(1)
            minute = match.group(2)

            # 小时转换
            hour_num = int(hour)
            if hour_num == 0:
                hour_chinese = "零"
            else:
                hour_chinese = self._convert_integer_to_chinese(str(hour_num))

            # 分钟转换
            minute_num = int(minute)
            if minute_num == 0:
                minute_chinese = ""  # 整点不读分钟
                return f"{hour_chinese}点"
            else:
                minute_chinese = self._convert_integer_to_chinese(str(minute_num))
                return f"{hour_chinese}点{minute_chinese}分"

        text = re.sub(r'(\d{1,2}):(\d{2})', convert_time, text)

        # 3. 处理电话号码（7位以上连续数字）- 按位读出
        def convert_phone(match):
            phone = match.group()
            return ''.join(self.digit_map[d] for d in phone)

        text = re.sub(r'\b\d{7,}\b', convert_phone, text)

        # 4. 处理普通数字（包括小数）
        def convert_normal_number(match):
            number = match.group()

            # 处理小数
            if '.' in number:
                parts = number.split('.')
                integer_part = self._convert_integer_to_chinese(parts[0])
                decimal_part = '点' + ''.join(self.digit_map[d] for d in parts[1])
                return integer_part + decimal_part

            # 处理整数
            return self._convert_integer_to_chinese(number)

        # 匹配剩余的数字（包括小数）
        pattern = r'\d+(?:\.\d+)?'
        text = re.sub(pattern, convert_normal_number, text)

        return text
    
    def _convert_integer_to_chinese(self, number_str: str) -> str:
        """
        将整数转换为中文
        
        Args:
            number_str: 数字字符串
            
        Returns:
            中文数字
        """
        if not number_str or number_str == '0':
            return '零'
        
        number = int(number_str)
        
        # 特殊情况处理
        if number == 0:
            return '零'
        elif number < 10:
            return self.digit_map[str(number)]
        elif number < 100:
            return self._convert_tens(number)
        elif number < 1000:
            return self._convert_hundreds(number)
        elif number < 10000:
            return self._convert_thousands(number)
        elif number < 100000000:
            return self._convert_ten_thousands(number)
        else:
            # 简化处理大数字
            return ''.join(self.digit_map[d] for d in number_str)
    
    def _convert_tens(self, number: int) -> str:
        """转换十位数"""
        tens = number // 10
        ones = number % 10
        
        if tens == 1:
            result = '十'
        else:
            result = self.digit_map[str(tens)] + '十'
        
        if ones > 0:
            result += self.digit_map[str(ones)]
        
        return result
    
    def _convert_hundreds(self, number: int) -> str:
        """转换百位数"""
        hundreds = number // 100
        remainder = number % 100
        
        result = self.digit_map[str(hundreds)] + '百'
        
        if remainder > 0:
            if remainder < 10:
                result += '零' + self.digit_map[str(remainder)]
            else:
                result += self._convert_tens(remainder)
        
        return result
    
    def _convert_thousands(self, number: int) -> str:
        """转换千位数"""
        thousands = number // 1000
        remainder = number % 1000
        
        result = self.digit_map[str(thousands)] + '千'
        
        if remainder > 0:
            if remainder < 100:
                result += '零'
                if remainder < 10:
                    result += self.digit_map[str(remainder)]
                else:
                    result += self._convert_tens(remainder)
            else:
                result += self._convert_hundreds(remainder)
        
        return result
    
    def _convert_ten_thousands(self, number: int) -> str:
        """转换万位数"""
        wan = number // 10000
        remainder = number % 10000
        
        if wan < 10:
            result = self.digit_map[str(wan)] + '万'
        else:
            result = self._convert_integer_to_chinese(str(wan)) + '万'
        
        if remainder > 0:
            if remainder < 1000:
                result += '零'
            result += self._convert_integer_to_chinese(str(remainder))
        
        return result
    
    def add_language_tags(self, text: str, language: str = None) -> str:
        """
        添加语言标记
        
        Args:
            text: 输入文本
            language: 语言代码，如果为None则自动检测
            
        Returns:
            带语言标记的文本
        """
        if language is None:
            language = self.detect_language(text)
        
        if language in self.language_tags:
            tag = self.language_tags[language]
            return f"{tag}{text}"
        
        return text
    
    def add_emotion_tags(self, text: str, emotions: Dict[str, List[str]]) -> str:
        """
        添加情感标记
        
        Args:
            text: 输入文本
            emotions: 情感标记字典 {'strong': ['勇气', '智慧'], 'laughter': ['哈哈']}
            
        Returns:
            带情感标记的文本
        """
        result = text
        
        for emotion, words in emotions.items():
            if emotion in self.emotion_tags:
                if isinstance(self.emotion_tags[emotion], tuple):
                    start_tag, end_tag = self.emotion_tags[emotion]
                    for word in words:
                        result = result.replace(word, f"{start_tag}{word}{end_tag}")
                else:
                    tag = self.emotion_tags[emotion]
                    for word in words:
                        result = result.replace(word, f"{word}{tag}")
        
        return result
    
    def split_sentences(self, text: str, max_length: int = 50) -> List[str]:
        """
        智能分句处理，将长文本分割为适合TTS的短句

        Args:
            text: 输入文本（不包含语言标记）
            max_length: 最大句子长度

        Returns:
            句子列表
        """
        # 移除语言标记进行分句
        clean_text = text
        for tag in self.language_tags.values():
            clean_text = clean_text.replace(tag, '')

        # 1. 首先按强分句符号分句
        strong_endings = r'[。！？；.!?;]'
        sentences = re.split(strong_endings, clean_text)

        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]

        # 2. 处理所有句子，使用多层分句策略
        result = []
        for sentence in sentences:
            if len(sentence) <= max_length:
                result.append(sentence)
            else:
                # 使用智能分句
                sub_sentences = self._smart_split_long_sentence(sentence, max_length)
                result.extend(sub_sentences)

        # 3. 过滤空句子并去除首尾空白
        result = [s.strip() for s in result if s.strip()]

        return result

    def _smart_split_long_sentence(self, sentence: str, max_length: int) -> List[str]:
        """
        智能分割长句子，使用多种策略

        Args:
            sentence: 长句子
            max_length: 最大长度

        Returns:
            分句列表
        """
        # 策略1: 按逗号分句
        if '，' in sentence or ',' in sentence:
            return self._split_by_comma(sentence, max_length)

        # 策略2: 按语义标志词分句
        semantic_result = self._split_by_semantic_markers(sentence, max_length)
        if len(semantic_result) > 1:
            return semantic_result

        # 策略3: 按语法结构分句
        grammar_result = self._split_by_grammar_structure(sentence, max_length)
        if len(grammar_result) > 1:
            return grammar_result

        # 策略4: 强制按长度分句（保底策略）
        return self._split_by_length(sentence, max_length)

    def _split_by_comma(self, sentence: str, max_length: int) -> List[str]:
        """按逗号分句"""
        sub_sentences = re.split(r'[，,]', sentence)
        result = []
        current = ""

        for sub in sub_sentences:
            sub = sub.strip()
            if not sub:
                continue

            test_current = current + "，" + sub if current else sub

            if len(test_current) <= max_length:
                current = test_current
            else:
                if current:
                    result.append(current)

                if len(sub) > max_length:
                    # 子句过长，继续分割
                    result.extend(self._smart_split_long_sentence(sub, max_length))
                    current = ""
                else:
                    current = sub

        if current:
            result.append(current)

        return result

    def _split_by_semantic_markers(self, sentence: str, max_length: int) -> List[str]:
        """
        按语义标志词分句
        基于中文语法中的介词、连词、助词等进行分句
        """
        # 语义分句标志词（基于搜索资料整理）
        semantic_markers = {
            # 介词（表示时间、地点、方式等关系）
            '在': 1, '从': 1, '到': 1, '对': 1, '为': 1, '由': 1, '被': 1,
            '把': 1, '向': 1, '朝': 1, '往': 1, '于': 1, '按': 1, '照': 1,
            '根据': 2, '通过': 2, '经过': 2, '关于': 2, '对于': 2,

            # 连词（表示并列、转折、因果等关系）
            '和': 1, '与': 1, '及': 1, '而': 1, '但': 1, '却': 1, '然而': 2,
            '因此': 2, '所以': 2, '但是': 2, '而且': 2, '并且': 2, '以及': 2,
            '不过': 2, '然后': 2, '接着': 2, '同时': 2, '另外': 2,

            # 结构助词
            '的': 1, '地': 1, '得': 1,

            # 时间标志词
            '当': 1, '在...时': 3, '...后': 2, '...前': 2, '之后': 2, '之前': 2,
            '随着': 2, '伴随': 2,

            # 条件标志词
            '如果': 2, '假如': 2, '要是': 2, '只要': 2, '除非': 2, '无论': 2,
        }

        # 找到所有可能的分句点
        split_points = []

        for marker, length in semantic_markers.items():
            start = 0
            while True:
                pos = sentence.find(marker, start)
                if pos == -1:
                    break

                # 检查是否是完整的词（避免部分匹配）
                if pos > 0 and sentence[pos-1].isalnum():
                    start = pos + 1
                    continue
                if pos + length < len(sentence) and sentence[pos + length].isalnum():
                    start = pos + 1
                    continue

                # 添加分句点（在标志词之后）
                split_point = pos + length
                if split_point < len(sentence):
                    split_points.append((split_point, marker))

                start = pos + 1

        # 按位置排序
        split_points.sort(key=lambda x: x[0])

        # 根据长度限制选择最佳分句点
        result = []
        start = 0

        for point, marker in split_points:
            # 检查当前段落长度
            if point - start >= max_length * 0.7:  # 达到70%长度就考虑分句
                segment = sentence[start:point].strip()
                if len(segment) >= 5:  # 避免过短的句子
                    result.append(segment)
                    start = point

        # 添加最后一部分
        if start < len(sentence):
            last_segment = sentence[start:].strip()
            if last_segment:
                result.append(last_segment)

        return result if len(result) > 1 else [sentence]

    def _split_by_grammar_structure(self, sentence: str, max_length: int) -> List[str]:
        """
        按语法结构分句
        寻找主谓宾结构的边界
        """
        # 动词标志词（通常是谓语的核心）
        verb_markers = ['是', '有', '在', '做', '说', '看', '听', '想', '要', '能', '会', '可以', '应该', '必须']

        # 寻找动词位置
        verb_positions = []
        for verb in verb_markers:
            start = 0
            while True:
                pos = sentence.find(verb, start)
                if pos == -1:
                    break
                verb_positions.append(pos + len(verb))
                start = pos + 1

        # 按位置排序
        verb_positions.sort()

        # 根据动词位置分句
        result = []
        start = 0

        for pos in verb_positions:
            if pos - start >= max_length * 0.6:  # 达到60%长度考虑分句
                segment = sentence[start:pos].strip()
                if len(segment) >= 8:  # 语法结构需要更长的最小长度
                    result.append(segment)
                    start = pos

        # 添加最后一部分
        if start < len(sentence):
            last_segment = sentence[start:].strip()
            if last_segment:
                result.append(last_segment)

        return result if len(result) > 1 else [sentence]

    def _split_by_length(self, sentence: str, max_length: int) -> List[str]:
        """
        按长度强制分句（保底策略）
        尽量在合适的位置分句
        """
        result = []
        start = 0

        while start < len(sentence):
            end = start + max_length

            if end >= len(sentence):
                # 最后一段
                result.append(sentence[start:].strip())
                break

            # 尝试在合适的位置分句（避免在词中间分句）
            # 寻找最近的空格、标点或合适的字符
            best_split = end
            for i in range(end - 10, end + 1):
                if i < len(sentence) and sentence[i] in ' 　，。！？；：':
                    best_split = i + 1
                    break

            segment = sentence[start:best_split].strip()
            if segment:
                result.append(segment)

            start = best_split

        return result
    
    def clean_text(self, text: str) -> str:
        """
        清理文本，移除不必要的字符
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留基本标点）
        text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？；：""''（）【】《》、,.!?;:"\'()\[\]{}<>]', '', text)
        
        # 移除首尾空白
        text = text.strip()
        
        return text
    
    def process_text(self, text: str,
                    language: str = None,
                    convert_numbers: bool = True,
                    normalize_punct: bool = True,
                    add_lang_tag: bool = True,
                    emotions: Dict[str, List[str]] = None,
                    max_sentence_length: int = 50) -> Dict:
        """
        完整的文本预处理流程

        Args:
            text: 输入文本
            language: 目标语言
            convert_numbers: 是否转换数字
            normalize_punct: 是否规范化标点
            add_lang_tag: 是否添加语言标记
            emotions: 情感标记
            max_sentence_length: 最大句子长度

        Returns:
            处理结果字典
        """
        original_text = text

        # 1. 清理文本
        text = self.clean_text(text)

        # 2. 检测语言
        if language is None:
            language = self.detect_language(text)

        # 3. 数字转换（仅中文，在标点规范化之前）
        if convert_numbers and language == 'zh':
            text = self.convert_numbers_to_chinese(text)

        # 4. 标点符号规范化
        if normalize_punct:
            text = self.normalize_punctuation(text, language)

        # 5. 添加情感标记
        if emotions:
            text = self.add_emotion_tags(text, emotions)

        # 6. 先分句处理（在添加语言标记之前）
        sentences = self.split_sentences(text, max_sentence_length)

        # 7. 为每个句子添加语言标记
        if add_lang_tag:
            processed_sentences = []
            lang_tag = self.language_tags.get(language, '')
            for sentence in sentences:
                processed_sentences.append(f"{lang_tag}{sentence}")
            sentences = processed_sentences

            # 完整文本也添加语言标记
            text = self.add_language_tags(text, language)

        return {
            'original_text': original_text,
            'processed_text': text,
            'language': language,
            'sentences': sentences,
            'sentence_count': len(sentences)
        }

def main():
    """测试函数"""
    processor = TextProcessor()

    # 测试用例
    test_texts = [
        "你好，我是通义生成式语音大模型，请问有什么可以帮您的吗？",
        "今天是2024年8月1日，温度是25.5度。",
        "现在时间是14:30，请拨打电话13812345678联系我们。",
        # 中文长句分句测试
        "人工智能技术的快速发展正在深刻改变着我们的生活方式，从智能手机到自动驾驶汽车，从语音助手到机器翻译，AI技术已经渗透到社会的各个角落，为人类带来了前所未有的便利和效率提升。",
        "春天来了！万物复苏，花儿开放；鸟儿歌唱，蝴蝶飞舞。大地披上了绿色的新装，到处都是生机勃勃的景象，让人心情愉悦，充满希望。",
        "在这个信息爆炸的时代，我们每天都要处理大量的数据和信息，如何从中提取有价值的内容，如何保持专注和思考能力，如何在快节奏的生活中找到内心的平静，这些都是现代人需要面对的重要课题。",
        # 测试没有逗号的长句
        "随着科技的不断进步和社会的快速发展人们对于生活质量的要求越来越高同时也面临着更多的挑战和机遇需要我们用智慧和勇气去面对",
        # 测试语义标志词分句
        "当我们在学习新知识的时候应该保持开放的心态并且要有耐心因为学习是一个循序渐进的过程不能急于求成"
    ]

    for text in test_texts:
        print(f"\n原文: {text}")
        print(f"长度: {len(text)} 字符")
        result = processor.process_text(text)
        print(f"语言: {result['language']}")
        print(f"分句: {result['sentences']}")
        print(f"分句数量: {len(result['sentences'])}")
        print("-" * 80)

if __name__ == "__main__":
    main()
