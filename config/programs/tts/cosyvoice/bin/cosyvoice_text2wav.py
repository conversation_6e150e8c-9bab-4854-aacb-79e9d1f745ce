#!/usr/bin/env python3
"""
CosyVoice TTS 主程序
将文本转换为 WAV 音频文件
"""

import sys
import os
import argparse
import logging
import json
import tempfile
from pathlib import Path
import numpy as np

# 添加父目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from cosyvoice_tts import CosyVoiceTTS
from text_processor import TextProcessor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="CosyVoice TTS - 文本转语音")
    
    # 基本参数
    parser.add_argument("text", help="要合成的文本")
    parser.add_argument("--output", "-o", required=True, help="输出音频文件路径")
    
    # 模型参数
    parser.add_argument("--model", default="models/tts/CosyVoice-300M-SFT", 
                       help="模型路径")
    parser.add_argument("--model-type", choices=["sft", "base", "instruct"], 
                       default="sft", help="模型类型")
    
    # 音色参数
    parser.add_argument("--speaker", default="中文女", 
                       help="音色名称（SFT模式）")
    parser.add_argument("--prompt-text", help="参考音频文本（零样本模式）")
    parser.add_argument("--prompt-audio", help="参考音频文件（零样本模式）")
    parser.add_argument("--instruct", help="指令描述（指令模式）")
    
    # 处理参数
    parser.add_argument("--language", choices=["zh", "en", "jp", "yue", "ko"],
                       help="语言代码（自动检测如果未指定）")
    parser.add_argument("--max-length", type=int, default=50,
                       help="最大句子长度")
    parser.add_argument("--stream", action="store_true",
                       help="流式合成")
    
    # 输出参数
    parser.add_argument("--sample-rate", type=int, default=22050,
                       help="输出采样率")
    parser.add_argument("--format", choices=["wav", "mp3"], default="wav",
                       help="输出格式")
    
    # 调试参数
    parser.add_argument("--debug", action="store_true", help="调试模式")
    parser.add_argument("--list-speakers", action="store_true", 
                       help="列出可用音色")
    
    return parser.parse_args()

def load_prompt_audio(audio_path: str) -> np.ndarray:
    """加载参考音频"""
    try:
        import soundfile as sf
        audio, sr = sf.read(audio_path)
        
        # 转换为16kHz单声道
        if sr != 16000:
            import librosa
            audio = librosa.resample(audio, orig_sr=sr, target_sr=16000)
        
        if audio.ndim > 1:
            audio = audio.mean(axis=1)  # 转为单声道
        
        return audio.astype(np.float32)
        
    except Exception as e:
        logger.error(f"加载参考音频失败: {e}")
        raise

def determine_synthesis_mode(args):
    """确定合成模式"""
    if args.prompt_audio and args.prompt_text:
        return "zero_shot"
    elif args.instruct:
        return "instruct"
    else:
        return "sft"

def synthesize_text(tts_service, text: str, args) -> dict:
    """合成文本"""
    mode = determine_synthesis_mode(args)
    
    try:
        if mode == "zero_shot":
            # 零样本模式
            prompt_audio = load_prompt_audio(args.prompt_audio)
            result = tts_service.synthesize_zero_shot(
                text=text,
                prompt_text=args.prompt_text,
                prompt_audio=prompt_audio,
                stream=args.stream
            )
        elif mode == "instruct":
            # 指令模式
            result = tts_service.synthesize_instruct(
                text=text,
                speaker=args.speaker,
                instruct=args.instruct,
                stream=args.stream
            )
        else:
            # SFT 模式
            result = tts_service.synthesize_sft(
                text=text,
                speaker=args.speaker,
                stream=args.stream
            )
        
        return result
        
    except Exception as e:
        logger.error(f"合成失败: {e}")
        raise

def process_long_text(tts_service, text: str, args) -> list:
    """处理长文本，分句合成"""
    processor = TextProcessor()
    
    # 文本预处理和分句
    processed = processor.process_text(
        text, 
        language=args.language,
        max_sentence_length=args.max_length
    )
    
    logger.info(f"文本分为 {len(processed['sentences'])} 个句子")
    
    # 逐句合成
    audio_segments = []
    total_duration = 0
    
    for i, sentence in enumerate(processed['sentences']):
        # 移除语言标记进行合成
        clean_sentence = sentence
        for tag in processor.language_tags.values():
            clean_sentence = clean_sentence.replace(tag, '')
        
        if not clean_sentence.strip():
            continue
        
        logger.info(f"合成第 {i+1}/{len(processed['sentences'])} 句: {clean_sentence}")
        
        try:
            result = synthesize_text(tts_service, clean_sentence, args)
            audio_segments.append(result['audio'])
            total_duration += result['audio_duration']
            
        except Exception as e:
            logger.error(f"合成第 {i+1} 句失败: {e}")
            # 继续处理其他句子
            continue
    
    if not audio_segments:
        raise RuntimeError("没有成功合成任何音频片段")
    
    # 拼接音频
    import torch
    combined_audio = torch.cat(audio_segments, dim=-1)
    
    return {
        'audio': combined_audio,
        'sample_rate': tts_service.sample_rate,
        'text': text,
        'processed_text': processed['processed_text'],
        'sentences': processed['sentences'],
        'audio_duration': total_duration,
        'synthesis_time': sum(seg.shape[-1] / tts_service.sample_rate for seg in audio_segments)
    }

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 初始化 TTS 服务
        logger.info(f"初始化 CosyVoice TTS 服务...")
        logger.info(f"模型路径: {args.model}")
        logger.info(f"模型类型: {args.model_type}")
        
        tts_service = CosyVoiceTTS(
            model_path=args.model,
            model_type=args.model_type,
            device="cpu"  # 可以根据需要改为 "cuda"
        )
        
        # 列出音色
        if args.list_speakers:
            speakers = tts_service.list_speakers()
            print("可用音色:")
            for speaker in speakers:
                print(f"  - {speaker}")
            return 0
        
        # 合成模式信息
        mode = determine_synthesis_mode(args)
        logger.info(f"合成模式: {mode}")
        
        if mode == "sft":
            logger.info(f"使用音色: {args.speaker}")
        elif mode == "zero_shot":
            logger.info(f"参考音频: {args.prompt_audio}")
            logger.info(f"参考文本: {args.prompt_text}")
        elif mode == "instruct":
            logger.info(f"基础音色: {args.speaker}")
            logger.info(f"指令: {args.instruct}")
        
        # 处理文本
        logger.info(f"输入文本: {args.text}")
        
        if len(args.text) > args.max_length:
            # 长文本分句处理
            result = process_long_text(tts_service, args.text, args)
        else:
            # 短文本直接处理
            result = synthesize_text(tts_service, args.text, args)
        
        # 保存音频
        logger.info(f"保存音频到: {args.output}")
        tts_service.save_audio(
            result['audio'], 
            args.output, 
            sample_rate=args.sample_rate
        )
        
        # 输出统计信息
        duration = result['audio_duration']
        synthesis_time = result.get('synthesis_time', duration)
        rtf = synthesis_time / duration if duration > 0 else 0
        
        logger.info(f"合成完成!")
        logger.info(f"音频时长: {duration:.2f}s")
        logger.info(f"合成耗时: {synthesis_time:.2f}s")
        logger.info(f"实时因子: {rtf:.3f}")
        
        # 输出 JSON 结果（用于程序化调用）
        if not args.debug:
            output_info = {
                "success": True,
                "output_file": args.output,
                "audio_duration": duration,
                "synthesis_time": synthesis_time,
                "rtf": rtf,
                "mode": mode,
                "speaker": args.speaker if mode in ["sft", "instruct"] else None
            }
            print(json.dumps(output_info, ensure_ascii=False))
        
        return 0
        
    except Exception as e:
        logger.error(f"TTS 合成失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        
        # 输出错误 JSON
        error_info = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(error_info, ensure_ascii=False))
        return 1

if __name__ == "__main__":
    exit(main())
