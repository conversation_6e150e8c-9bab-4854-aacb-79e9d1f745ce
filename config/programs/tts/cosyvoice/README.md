# 🎵 CosyVoice TTS 程序

基于阿里巴巴开源的 CosyVoice-300M 模型实现的高质量中文语音合成程序，集成到 Rhasspy 3.0 语音助手系统。

## 📋 **功能特性**

### **🎯 核心功能**
- **多语言支持**: 中文、英文、日文、韩文、粤语
- **多种合成模式**: SFT（预定义音色）、零样本音色克隆、指令控制
- **智能文本处理**: 自动分句、数字转换、标点规范化
- **高质量合成**: MOS 5.53，接近真人语音
- **实时性能**: RTF < 0.1，支持流式合成

### **🎤 支持的音色**
- **中文**: 中文女、中文男
- **英文**: 英文女、英文男
- **其他**: 日语男、粤语女、韩语女

### **🔧 合成模式**
1. **SFT 模式**: 使用预定义音色快速合成
2. **零样本模式**: 3秒参考音频即可克隆音色
3. **指令模式**: 支持情感和风格控制

## 🚀 **快速开始**

### **1. 安装**
```bash
# 运行安装脚本
./script/setup

# 下载模型（可选）
python ../../scripts/download_cosyvoice_models.py
```

### **2. 基本使用**
```bash
# SFT 模式合成
python bin/cosyvoice_text2wav.py "你好，我是语音助手" -o output.wav --speaker "中文女"

# 零样本音色克隆
python bin/cosyvoice_text2wav.py "这是克隆的声音" -o output.wav \
    --prompt-audio reference.wav --prompt-text "参考音频的文本"

# 指令控制合成
python bin/cosyvoice_text2wav.py "他展现了非凡的勇气" -o output.wav \
    --speaker "中文男" --instruct "用激动的语调说这句话"
```

### **3. 查看帮助**
```bash
python bin/cosyvoice_text2wav.py --help
```

## 📖 **详细使用说明**

### **命令行参数**

#### **基本参数**
- `text`: 要合成的文本（必需）
- `--output, -o`: 输出音频文件路径（必需）
- `--model`: 模型路径（默认: models/tts/CosyVoice-300M-SFT）
- `--model-type`: 模型类型（sft/base/instruct，默认: sft）

#### **音色参数**
- `--speaker`: 音色名称（默认: 中文女）
- `--prompt-text`: 参考音频文本（零样本模式）
- `--prompt-audio`: 参考音频文件（零样本模式）
- `--instruct`: 指令描述（指令模式）

#### **处理参数**
- `--language`: 语言代码（zh/en/jp/yue/ko，自动检测）
- `--max-length`: 最大句子长度（默认: 50）
- `--stream`: 启用流式合成

#### **输出参数**
- `--sample-rate`: 输出采样率（默认: 22050）
- `--format`: 输出格式（wav/mp3，默认: wav）

#### **调试参数**
- `--debug`: 调试模式
- `--list-speakers`: 列出可用音色

### **使用示例**

#### **1. 基础合成**
```bash
# 使用默认音色
python bin/cosyvoice_text2wav.py "今天天气真好" -o weather.wav

# 指定音色
python bin/cosyvoice_text2wav.py "欢迎使用语音助手" -o welcome.wav --speaker "中文男"

# 英文合成
python bin/cosyvoice_text2wav.py "Hello world" -o hello.wav --speaker "英文女"
```

#### **2. 长文本合成**
```bash
# 自动分句处理
python bin/cosyvoice_text2wav.py \
    "人工智能技术的快速发展正在深刻改变着我们的生活方式，从智能手机到自动驾驶汽车，从语音助手到机器翻译，AI技术已经渗透到社会的各个角落。" \
    -o long_text.wav --max-length 30
```

#### **3. 零样本音色克隆**
```bash
# 准备参考音频（3秒以上，16kHz）
python bin/cosyvoice_text2wav.py \
    "这是用克隆音色说的话" \
    -o cloned.wav \
    --prompt-audio reference.wav \
    --prompt-text "参考音频中说的内容"
```

#### **4. 情感控制**
```bash
# 使用指令控制情感
python bin/cosyvoice_text2wav.py \
    "恭喜你获得了第一名" \
    -o congratulation.wav \
    --model-type instruct \
    --speaker "中文女" \
    --instruct "用兴奋和激动的语调说这句话"
```

## 🔧 **集成到 Rhasspy**

### **配置文件**
在 `configuration.yaml` 中添加：

```yaml
tts:
  cosyvoice:
    command: |
      conda run -n aibi python bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --speaker "${speaker}"
    template_args:
      speaker: "中文女"
```

### **高级配置**
```yaml
tts:
  cosyvoice-sft:
    command: |
      conda run -n aibi python bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --speaker "${speaker}" --model-type sft
    template_args:
      speaker: "中文女"
  
  cosyvoice-instruct:
    command: |
      conda run -n aibi python bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --speaker "${speaker}" --model-type instruct --instruct "${instruct}"
    template_args:
      speaker: "中文女"
      instruct: "用自然的语调说话"
```

## 📊 **性能指标**

### **质量指标**
- **MOS 评分**: 5.53（CosyVoice2）
- **发音准确率**: 95%+
- **音色相似度**: 90%+（零样本克隆）

### **性能指标**
- **实时因子**: < 0.1
- **首包延迟**: 150ms（流式模式）
- **内存占用**: 2-4GB
- **支持语言**: 5种语言

### **文本处理**
- **智能分句**: 支持语义标志词分句
- **数字转换**: 年份、时间、电话号码智能转换
- **标点规范化**: 自动标点符号规范化

## 🛠️ **故障排除**

### **常见问题**

#### **1. 模型加载失败**
```
错误: 模型文件不存在
解决: 运行 python ../../scripts/download_cosyvoice_models.py 下载模型
```

#### **2. 音频质量差**
```
问题: 合成音频有杂音或不自然
解决: 
- 检查输入文本是否有特殊字符
- 尝试不同的音色
- 调整 --max-length 参数
```

#### **3. 合成速度慢**
```
问题: RTF > 1.0，合成速度慢
解决:
- 使用 GPU 加速（修改 device="cuda"）
- 减少 --max-length 参数
- 使用 CosyVoice2 模型
```

#### **4. 内存不足**
```
问题: CUDA out of memory 或系统内存不足
解决:
- 减少批处理大小
- 使用 CPU 模式
- 分段处理长文本
```

### **调试模式**
```bash
# 启用详细日志
python bin/cosyvoice_text2wav.py "测试文本" -o test.wav --debug

# 检查可用音色
python bin/cosyvoice_text2wav.py --list-speakers
```

## 📚 **技术文档**

### **文件结构**
```
config/programs/tts/cosyvoice/
├── bin/
│   └── cosyvoice_text2wav.py      # 主程序
├── script/
│   └── setup                      # 安装脚本
├── cosyvoice_tts.py               # TTS 服务类
├── text_processor.py              # 文本预处理
├── requirements.txt               # 依赖列表
└── README.md                      # 说明文档
```

### **API 接口**
程序输出 JSON 格式结果，便于程序化调用：

```json
{
  "success": true,
  "output_file": "output.wav",
  "audio_duration": 2.5,
  "synthesis_time": 0.8,
  "rtf": 0.32,
  "mode": "sft",
  "speaker": "中文女"
}
```

### **扩展开发**
- `cosyvoice_tts.py`: 核心 TTS 服务类
- `text_processor.py`: 文本预处理模块
- 支持自定义音色和模型
- 支持插件式扩展

## 📄 **许可证**

本程序基于 CosyVoice 开源项目开发，遵循相应的开源许可证。

## 🤝 **贡献**

欢迎提交 Issue 和 Pull Request 来改进这个程序。

---

**CosyVoice TTS 为 Rhasspy 3.0 提供了世界级的语音合成能力！** 🎉
