#!/bin/bash
# CosyVoice TTS 安装脚本

set -e

echo "🎵 开始安装 CosyVoice TTS..."

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROGRAM_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(cd "$PROGRAM_DIR/../../../.." && pwd)"

echo "程序目录: $PROGRAM_DIR"
echo "项目根目录: $PROJECT_ROOT"

# 检查 conda 环境
if ! conda info --envs | grep -q "aibi"; then
    echo "❌ 未找到 aibi conda 环境"
    echo "请先创建 aibi 环境: conda create -n aibi python=3.10"
    exit 1
fi

echo "✅ 找到 aibi conda 环境"

# 激活环境并安装依赖
echo "📦 安装 Python 依赖..."
conda run -n aibi pip install -r "$PROGRAM_DIR/requirements.txt"

# 检查 CosyVoice 源码
COSYVOICE_DIR="$PROGRAM_DIR/third_party/CosyVoice"
if [ ! -d "$COSYVOICE_DIR" ]; then
    echo "📥 下载 CosyVoice 源码..."
    mkdir -p "$PROGRAM_DIR/third_party"
    cd "$PROGRAM_DIR/third_party"
    
    # 克隆 CosyVoice 仓库
    git clone https://github.com/FunAudioLLM/CosyVoice.git
    cd CosyVoice
    
    # 安装 CosyVoice 依赖
    echo "📦 安装 CosyVoice 依赖..."
    conda run -n aibi pip install -r requirements.txt
    
    # 安装第三方依赖
    if [ -d "third_party/Matcha-TTS" ]; then
        cd third_party/Matcha-TTS
        conda run -n aibi pip install -e .
        cd ../..
    fi
else
    echo "✅ CosyVoice 源码已存在"
fi

# 检查模型文件
MODEL_DIR="$PROJECT_ROOT/models/tts"
if [ ! -d "$MODEL_DIR" ]; then
    echo "📁 创建模型目录..."
    mkdir -p "$MODEL_DIR"
fi

# 检查是否有模型文件
if [ ! -d "$MODEL_DIR/CosyVoice-300M-SFT" ]; then
    echo "⚠️  未找到 CosyVoice 模型文件"
    echo "请运行以下命令下载模型:"
    echo "  python scripts/download_cosyvoice_models.py"
    echo ""
    echo "或者手动下载模型到 $MODEL_DIR/"
else
    echo "✅ 找到 CosyVoice 模型文件"
fi

# 设置可执行权限
echo "🔧 设置可执行权限..."
chmod +x "$PROGRAM_DIR/bin/cosyvoice_text2wav.py"

# 测试安装
echo "🧪 测试安装..."
cd "$PROGRAM_DIR"

# 测试文本处理
echo "测试文本处理..."
if conda run -n aibi python text_processor.py > /dev/null 2>&1; then
    echo "✅ 文本处理模块正常"
else
    echo "❌ 文本处理模块测试失败"
    exit 1
fi

# 测试主程序帮助
echo "测试主程序..."
if conda run -n aibi python bin/cosyvoice_text2wav.py --help > /dev/null 2>&1; then
    echo "✅ 主程序正常"
else
    echo "❌ 主程序测试失败"
    exit 1
fi

echo ""
echo "🎉 CosyVoice TTS 安装完成!"
echo ""
echo "📋 使用说明:"
echo "  1. 下载模型: python scripts/download_cosyvoice_models.py"
echo "  2. 测试合成: python bin/cosyvoice_text2wav.py \"你好世界\" -o test.wav"
echo "  3. 查看帮助: python bin/cosyvoice_text2wav.py --help"
echo ""
echo "🔧 配置 Rhasspy:"
echo "  在 configuration.yaml 中配置 cosyvoice TTS 程序"
echo ""
