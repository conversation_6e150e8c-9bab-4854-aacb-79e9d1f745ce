#!/usr/bin/env python3
"""
CosyVoice TTS 服务类
基于官方 CosyVoice 模型实现的 TTS 服务
"""

import sys
import os
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Generator
import numpy as np
import torch
import torchaudio
import soundfile as sf

# 添加 CosyVoice 和 Matcha-TTS 路径
current_dir = Path(__file__).parent
cosyvoice_path = current_dir / "third_party" / "CosyVoice"
matcha_path = current_dir / "third_party" / "Matcha-TTS"

if cosyvoice_path.exists():
    sys.path.insert(0, str(cosyvoice_path))
    print(f"添加 CosyVoice 路径: {cosyvoice_path}")

if matcha_path.exists():
    sys.path.insert(0, str(matcha_path))
    print(f"添加 Matcha-TTS 路径: {matcha_path}")

from text_processor import TextProcessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CosyVoiceTTS:
    """CosyVoice TTS 服务类"""
    
    def __init__(self, model_path: str, model_type: str = "sft", device: str = "cpu"):
        """
        初始化 CosyVoice TTS 服务
        
        Args:
            model_path: 模型路径
            model_type: 模型类型 (sft/base/instruct)
            device: 设备 (cpu/cuda)
        """
        self.model_path = Path(model_path)
        self.model_type = model_type
        self.device = device
        self.model = None
        self.sample_rate = 22050
        self.text_processor = TextProcessor()
        
        # 预定义音色（SFT 模式）
        self.available_speakers = [
            "中文女", "中文男", "英文女", "英文男", 
            "日语男", "粤语女", "韩语女"
        ]
        
        # 初始化模型
        self._load_model()
    
    def _load_model(self):
        """加载 CosyVoice 模型"""
        try:
            logger.info(f"正在加载 CosyVoice 模型: {self.model_path}")
            start_time = time.time()

            # 按照官方文档方式导入 CosyVoice
            try:
                # 确保 Matcha-TTS 在路径中
                matcha_path = Path(__file__).parent / "third_party" / "CosyVoice" / "third_party" / "Matcha-TTS"
                if matcha_path.exists() and str(matcha_path) not in sys.path:
                    sys.path.insert(0, str(matcha_path))

                from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
                
                # 根据模型类型选择合适的类
                if "CosyVoice2" in str(self.model_path):
                    self.model = CosyVoice2(
                        str(self.model_path),
                        load_jit=False,
                        load_trt=False,
                        load_vllm=False,
                        fp16=False
                    )
                    logger.info("使用 CosyVoice2 模型")
                else:
                    self.model = CosyVoice(
                        str(self.model_path),
                        load_jit=False,
                        load_trt=False,
                        fp16=False
                    )
                    logger.info("使用 CosyVoice 模型")
                
                # 获取采样率
                self.sample_rate = self.model.sample_rate
                
                # 获取可用音色（SFT 模式）
                if self.model_type == "sft" and hasattr(self.model, 'list_available_spks'):
                    try:
                        self.available_speakers = self.model.list_available_spks()
                        logger.info(f"可用音色: {self.available_speakers}")
                    except Exception as e:
                        logger.warning(f"获取音色列表失败: {e}")
                
                load_time = time.time() - start_time
                logger.info(f"模型加载完成，耗时: {load_time:.2f}s")
                
            except ImportError as e:
                logger.error(f"导入 CosyVoice 失败: {e}")
                logger.error("请确保已正确安装 CosyVoice 依赖")
                raise
                
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def list_speakers(self) -> List[str]:
        """获取可用音色列表"""
        return self.available_speakers.copy()
    
    def synthesize_sft(self, text: str, speaker: str = "中文女", stream: bool = False) -> Dict:
        """
        SFT 模式语音合成（预定义音色）
        
        Args:
            text: 输入文本
            speaker: 音色名称
            stream: 是否流式合成
            
        Returns:
            合成结果
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        if speaker not in self.available_speakers:
            logger.warning(f"音色 {speaker} 不可用，使用默认音色")
            speaker = self.available_speakers[0] if self.available_speakers else "中文女"
        
        try:
            logger.info(f"SFT 合成: 文本='{text}', 音色='{speaker}'")
            start_time = time.time()
            
            # 文本预处理
            processed = self.text_processor.process_text(text)
            
            # 合成语音
            audio_chunks = []
            for i, chunk in enumerate(self.model.inference_sft(
                processed['processed_text'], 
                speaker, 
                stream=stream
            )):
                audio_chunks.append(chunk['tts_speech'])
                if not stream:
                    break
            
            # 拼接音频
            if audio_chunks:
                audio = torch.cat(audio_chunks, dim=-1)
            else:
                audio = torch.zeros(1, 1000)  # 空音频
            
            synthesis_time = time.time() - start_time
            audio_duration = audio.shape[-1] / self.sample_rate
            rtf = synthesis_time / audio_duration if audio_duration > 0 else 0
            
            logger.info(f"SFT 合成完成: 耗时={synthesis_time:.2f}s, RTF={rtf:.3f}")
            
            return {
                'audio': audio,
                'sample_rate': self.sample_rate,
                'text': text,
                'processed_text': processed['processed_text'],
                'speaker': speaker,
                'synthesis_time': synthesis_time,
                'audio_duration': audio_duration,
                'rtf': rtf
            }
            
        except Exception as e:
            logger.error(f"SFT 合成失败: {e}")
            raise
    
    def synthesize_zero_shot(self, text: str, prompt_text: str, prompt_audio: np.ndarray, stream: bool = False) -> Dict:
        """
        零样本语音合成（音色克隆）
        
        Args:
            text: 要合成的文本
            prompt_text: 参考音频对应的文本
            prompt_audio: 参考音频数据 (16kHz)
            stream: 是否流式合成
            
        Returns:
            合成结果
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        try:
            logger.info(f"零样本合成: 文本='{text}', 参考文本='{prompt_text}'")
            start_time = time.time()
            
            # 文本预处理
            processed = self.text_processor.process_text(text)
            
            # 转换参考音频格式
            if isinstance(prompt_audio, np.ndarray):
                prompt_audio = torch.from_numpy(prompt_audio).float()
            
            # 确保音频是正确的形状
            if prompt_audio.dim() == 1:
                prompt_audio = prompt_audio.unsqueeze(0)
            
            # 合成语音
            audio_chunks = []
            for i, chunk in enumerate(self.model.inference_zero_shot(
                processed['processed_text'],
                prompt_text,
                prompt_audio,
                stream=stream
            )):
                audio_chunks.append(chunk['tts_speech'])
                if not stream:
                    break
            
            # 拼接音频
            if audio_chunks:
                audio = torch.cat(audio_chunks, dim=-1)
            else:
                audio = torch.zeros(1, 1000)
            
            synthesis_time = time.time() - start_time
            audio_duration = audio.shape[-1] / self.sample_rate
            rtf = synthesis_time / audio_duration if audio_duration > 0 else 0
            
            logger.info(f"零样本合成完成: 耗时={synthesis_time:.2f}s, RTF={rtf:.3f}")
            
            return {
                'audio': audio,
                'sample_rate': self.sample_rate,
                'text': text,
                'processed_text': processed['processed_text'],
                'prompt_text': prompt_text,
                'synthesis_time': synthesis_time,
                'audio_duration': audio_duration,
                'rtf': rtf
            }
            
        except Exception as e:
            logger.error(f"零样本合成失败: {e}")
            raise
    
    def synthesize_instruct(self, text: str, speaker: str, instruct: str, stream: bool = False) -> Dict:
        """
        指令模式语音合成（情感控制）
        
        Args:
            text: 要合成的文本
            speaker: 基础音色
            instruct: 指令描述
            stream: 是否流式合成
            
        Returns:
            合成结果
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        try:
            logger.info(f"指令合成: 文本='{text}', 音色='{speaker}', 指令='{instruct}'")
            start_time = time.time()
            
            # 文本预处理
            processed = self.text_processor.process_text(text)
            
            # 合成语音
            audio_chunks = []
            for i, chunk in enumerate(self.model.inference_instruct(
                processed['processed_text'],
                speaker,
                instruct,
                stream=stream
            )):
                audio_chunks.append(chunk['tts_speech'])
                if not stream:
                    break
            
            # 拼接音频
            if audio_chunks:
                audio = torch.cat(audio_chunks, dim=-1)
            else:
                audio = torch.zeros(1, 1000)
            
            synthesis_time = time.time() - start_time
            audio_duration = audio.shape[-1] / self.sample_rate
            rtf = synthesis_time / audio_duration if audio_duration > 0 else 0
            
            logger.info(f"指令合成完成: 耗时={synthesis_time:.2f}s, RTF={rtf:.3f}")
            
            return {
                'audio': audio,
                'sample_rate': self.sample_rate,
                'text': text,
                'processed_text': processed['processed_text'],
                'speaker': speaker,
                'instruct': instruct,
                'synthesis_time': synthesis_time,
                'audio_duration': audio_duration,
                'rtf': rtf
            }
            
        except Exception as e:
            logger.error(f"指令合成失败: {e}")
            raise
    
    def save_audio(self, audio: torch.Tensor, output_path: str, sample_rate: int = None):
        """
        保存音频文件
        
        Args:
            audio: 音频张量
            output_path: 输出路径
            sample_rate: 采样率
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        try:
            # 确保音频格式正确
            if audio.dim() == 1:
                audio = audio.unsqueeze(0)
            
            # 保存音频
            torchaudio.save(output_path, audio.cpu(), sample_rate)
            logger.info(f"音频已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"保存音频失败: {e}")
            raise
    
    def synthesize(self, text: str, mode: str = "sft", **kwargs) -> Dict:
        """
        统一的语音合成接口
        
        Args:
            text: 输入文本
            mode: 合成模式 (sft/zero_shot/instruct)
            **kwargs: 其他参数
            
        Returns:
            合成结果
        """
        if mode == "sft":
            speaker = kwargs.get('speaker', '中文女')
            stream = kwargs.get('stream', False)
            return self.synthesize_sft(text, speaker, stream)
        
        elif mode == "zero_shot":
            prompt_text = kwargs.get('prompt_text', '')
            prompt_audio = kwargs.get('prompt_audio')
            stream = kwargs.get('stream', False)
            
            if prompt_audio is None:
                raise ValueError("零样本模式需要提供 prompt_audio")
            
            return self.synthesize_zero_shot(text, prompt_text, prompt_audio, stream)
        
        elif mode == "instruct":
            speaker = kwargs.get('speaker', '中文女')
            instruct = kwargs.get('instruct', '')
            stream = kwargs.get('stream', False)
            return self.synthesize_instruct(text, speaker, instruct, stream)
        
        else:
            raise ValueError(f"不支持的合成模式: {mode}")

def main():
    """测试函数"""
    # 这里需要实际的模型路径
    model_path = "models/tts/CosyVoice-300M-SFT"
    
    if not Path(model_path).exists():
        print(f"模型路径不存在: {model_path}")
        print("请先下载 CosyVoice 模型")
        return
    
    try:
        # 初始化 TTS 服务
        tts = CosyVoiceTTS(model_path, model_type="sft")
        
        # 测试 SFT 合成
        text = "你好，我是通义生成式语音大模型，请问有什么可以帮您的吗？"
        result = tts.synthesize_sft(text, "中文女")
        
        print(f"合成完成:")
        print(f"  文本: {result['text']}")
        print(f"  音色: {result['speaker']}")
        print(f"  耗时: {result['synthesis_time']:.2f}s")
        print(f"  RTF: {result['rtf']:.3f}")
        
        # 保存音频
        output_path = "test_output.wav"
        tts.save_audio(result['audio'], output_path)
        print(f"  输出: {output_path}")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    main()
