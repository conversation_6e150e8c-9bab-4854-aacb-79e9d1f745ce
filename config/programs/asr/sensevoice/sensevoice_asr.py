#!/usr/bin/env python3
"""
SenseVoiceSmall ASR 服务
基于阿里达摩院 SenseVoiceSmall 模型的语音识别服务
"""

import logging
import time
from pathlib import Path
from typing import Optional, Dict, Any
import tempfile

# 导入 FunASR 相关库
try:
    from funasr import AutoModel
    from modelscope import snapshot_download
except ImportError as e:
    logging.error(f"请安装 FunASR 和 ModelScope: pip install funasr modelscope")
    raise e

from audio_processor import AudioProcessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SenseVoiceASR:
    """
    SenseVoiceSmall ASR 服务
    
    提供基于 SenseVoiceSmall 模型的语音识别功能
    """
    
    def __init__(
        self,
        model_id: str = "iic/SenseVoiceSmall",
        device: str = "cpu",
        cache_dir: str = "./models",
        language: str = "auto",
        use_vad: bool = True,
        use_punc: bool = True,
        use_itn: bool = True
    ):
        """
        初始化 SenseVoice ASR 服务
        
        Args:
            model_id: 模型 ID
            device: 计算设备 ("cpu" 或 "cuda")
            cache_dir: 模型缓存目录
            language: 识别语言 ("auto", "zh", "en", 等)
            use_vad: 是否使用 VAD
            use_punc: 是否使用标点预测
            use_itn: 是否使用逆文本归一化
        """
        self.model_id = model_id
        self.device = device
        self.cache_dir = cache_dir
        self.language = language
        self.use_vad = use_vad
        self.use_punc = use_punc
        self.use_itn = use_itn
        
        # 初始化音频处理器
        self.audio_processor = AudioProcessor(vad_enabled=False)  # SenseVoice 自带 VAD
        
        # 模型相关
        self.model = None
        self.model_loaded = False
        
        # 统计信息
        self.recognition_count = 0
        self.total_audio_duration = 0.0
        self.total_processing_time = 0.0
        
        logger.info(f"SenseVoice ASR 服务初始化:")
        logger.info(f"  模型: {model_id}")
        logger.info(f"  设备: {device}")
        logger.info(f"  语言: {language}")
        logger.info(f"  VAD: {use_vad}")
        logger.info(f"  标点: {use_punc}")
        logger.info(f"  ITN: {use_itn}")
    
    def _download_model(self) -> str:
        """下载模型到本地"""
        try:
            logger.info(f"下载模型: {self.model_id}")
            model_dir = snapshot_download(
                self.model_id,
                cache_dir=self.cache_dir
            )
            logger.info(f"模型下载完成: {model_dir}")
            return model_dir
        except Exception as e:
            logger.error(f"模型下载失败: {e}")
            raise
    
    def load_model(self):
        """加载 SenseVoice 模型"""
        if self.model_loaded:
            return
        
        try:
            logger.info("加载 SenseVoice 模型...")
            start_time = time.time()
            
            # 构建模型参数
            model_kwargs = {
                "model": self.model_id,
                "device": self.device,
            }
            
            # 添加 VAD 模型（如果启用）
            if self.use_vad:
                model_kwargs["vad_model"] = "fsmn-vad"
            
            # 添加标点预测模型（如果启用）
            if self.use_punc:
                model_kwargs["punc_model"] = "ct-punc"
            
            # 加载模型
            self.model = AutoModel(**model_kwargs)
            
            load_time = time.time() - start_time
            self.model_loaded = True
            
            logger.info(f"模型加载完成，耗时: {load_time:.2f}s")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def recognize_file(self, audio_file: str) -> Dict[str, Any]:
        """
        识别音频文件
        
        Args:
            audio_file: 音频文件路径
            
        Returns:
            识别结果字典
        """
        if not self.model_loaded:
            self.load_model()
        
        try:
            start_time = time.time()
            
            # 预处理音频
            processed_audio = self.audio_processor.process_for_sensevoice(
                audio_file, 
                input_type="file"
            )
            
            # 运行识别
            result = self.model.generate(
                input=processed_audio,
                language=self.language,
                use_itn=self.use_itn,
                batch_size_s=300,  # 批处理大小（秒）
            )
            
            processing_time = time.time() - start_time
            
            # 解析结果
            if result and len(result) > 0:
                text = result[0].get("text", "").strip()
                language = result[0].get("language", self.language)
                timestamp = result[0].get("timestamp", [])
                
                # 计算音频时长
                import librosa
                audio_duration = librosa.get_duration(filename=processed_audio)
                
                # 更新统计信息
                self.recognition_count += 1
                self.total_audio_duration += audio_duration
                self.total_processing_time += processing_time
                
                # 清理临时文件
                if processed_audio != audio_file:
                    Path(processed_audio).unlink(missing_ok=True)
                
                # 返回结果
                recognition_result = {
                    "text": text,
                    "language": language,
                    "timestamp": timestamp,
                    "audio_duration": audio_duration,
                    "processing_time": processing_time,
                    "real_time_factor": processing_time / audio_duration if audio_duration > 0 else 0,
                    "model": "SenseVoiceSmall"
                }
                
                logger.info(f"识别完成: '{text}' (耗时: {processing_time:.2f}s)")
                return recognition_result
            else:
                logger.warning("识别结果为空")
                return {
                    "text": "",
                    "language": self.language,
                    "error": "识别结果为空"
                }
                
        except Exception as e:
            logger.error(f"语音识别失败: {e}")
            return {
                "text": "",
                "language": self.language,
                "error": str(e)
            }
    
    def recognize_raw_audio(
        self, 
        raw_audio: bytes, 
        sample_rate: int = 16000,
        sample_width: int = 2
    ) -> Dict[str, Any]:
        """
        识别原始音频数据
        
        Args:
            raw_audio: 原始音频字节数据
            sample_rate: 采样率
            sample_width: 样本宽度
            
        Returns:
            识别结果字典
        """
        try:
            # 转换原始音频为数组
            audio_array = self.audio_processor.convert_raw_audio(
                raw_audio, sample_rate, sample_width
            )
            
            # 创建临时文件
            temp_wav = self.audio_processor.create_temp_wav(audio_array)
            
            # 识别
            result = self.recognize_file(temp_wav)
            
            # 清理临时文件
            Path(temp_wav).unlink(missing_ok=True)
            
            return result
            
        except Exception as e:
            logger.error(f"原始音频识别失败: {e}")
            return {
                "text": "",
                "language": self.language,
                "error": str(e)
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        avg_processing_time = (
            self.total_processing_time / self.recognition_count 
            if self.recognition_count > 0 else 0
        )
        avg_real_time_factor = (
            self.total_processing_time / self.total_audio_duration 
            if self.total_audio_duration > 0 else 0
        )
        
        return {
            "recognition_count": self.recognition_count,
            "total_audio_duration": self.total_audio_duration,
            "total_processing_time": self.total_processing_time,
            "avg_processing_time": avg_processing_time,
            "avg_real_time_factor": avg_real_time_factor,
            "model_loaded": self.model_loaded
        }
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
            self.model_loaded = False
        logger.info("ASR 服务资源已清理")


def test_sensevoice_asr():
    """测试 SenseVoice ASR 服务"""
    print("=== 测试 SenseVoice ASR 服务 ===")
    
    try:
        # 创建 ASR 服务
        asr = SenseVoiceASR(
            device="cpu",
            language="auto",
            use_vad=True,
            use_punc=True
        )
        
        # 生成测试音频
        import numpy as np
        duration = 2.0
        sample_rate = 16000
        t = np.linspace(0, duration, int(sample_rate * duration))
        
        # 生成简单的测试信号
        audio = 0.1 * np.sin(2 * np.pi * 440 * t)  # 440Hz 正弦波
        
        # 创建临时 WAV 文件
        temp_wav = asr.audio_processor.create_temp_wav(audio)
        
        print(f"测试音频文件: {temp_wav}")
        
        # 测试识别
        print("开始语音识别测试...")
        result = asr.recognize_file(temp_wav)
        
        print("识别结果:")
        print(f"  文本: '{result.get('text', '')}'")
        print(f"  语言: {result.get('language', '')}")
        print(f"  处理时间: {result.get('processing_time', 0):.2f}s")
        print(f"  实时率: {result.get('real_time_factor', 0):.2f}")
        
        # 显示统计信息
        stats = asr.get_stats()
        print(f"\n服务统计:")
        print(f"  识别次数: {stats['recognition_count']}")
        print(f"  平均处理时间: {stats['avg_processing_time']:.2f}s")
        print(f"  平均实时率: {stats['avg_real_time_factor']:.2f}")
        
        # 清理
        Path(temp_wav).unlink(missing_ok=True)
        asr.cleanup()
        
        print("✅ SenseVoice ASR 服务测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_sensevoice_asr()
