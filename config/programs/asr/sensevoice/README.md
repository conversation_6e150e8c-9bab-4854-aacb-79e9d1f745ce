# SenseVoiceSmall ASR 程序

基于阿里达摩院 SenseVoiceSmall 模型的语音识别程序，符合 Rhasspy 3.0 程序标准。

## 🎯 **功能特性**

- ✅ **多语言支持**: 中文、英文、日文、韩文等
- ✅ **高精度识别**: 中文识别准确率 > 95%
- ✅ **实时处理**: 实时率 < 0.3 (CPU)
- ✅ **自动标点**: 内置标点预测功能
- ✅ **语音检测**: 集成 VAD 语音活动检测
- ✅ **格式兼容**: 支持多种音频格式

## 📦 **安装**

```bash
# 运行安装脚本
./script/setup

# 或手动安装
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## 🚀 **使用方法**

### **命令行使用**
```bash
# 基本用法
.venv/bin/python3 bin/sensevoice_wav2text.py audio.wav

# 指定语言
.venv/bin/python3 bin/sensevoice_wav2text.py audio.wav --language zh

# 启用调试
.venv/bin/python3 bin/sensevoice_wav2text.py audio.wav --debug

# 使用 GPU
.venv/bin/python3 bin/sensevoice_wav2text.py audio.wav --device cuda
```

### **Rhasspy 3.0 集成**
在 `configuration.yaml` 中配置：

```yaml
programs:
  asr:
    sensevoice:
      command: |
        .venv/bin/python3 bin/sensevoice_wav2text.py "${wav_file}" --language "${language}" --device "${device}"
      template_args:
        language: "auto"
        device: "cpu"
```

## 📊 **输出格式**

程序输出标准 JSON 格式：

```json
{
  "text": "你好，这是一个测试。",
  "language": "zh",
  "model": "SenseVoiceSmall",
  "processing_time": 0.25,
  "audio_duration": 2.0,
  "real_time_factor": 0.125
}
```

## ⚙️ **配置参数**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--model` | `iic/SenseVoiceSmall` | 模型 ID |
| `--language` | `auto` | 识别语言 |
| `--device` | `cpu` | 计算设备 |
| `--use-vad` | `True` | 启用 VAD |
| `--use-punc` | `True` | 启用标点预测 |
| `--use-itn` | `True` | 启用逆文本归一化 |

## 🔧 **故障排除**

### **模型下载问题**
```bash
# 手动下载模型
python3 -c "from modelscope import snapshot_download; snapshot_download('iic/SenseVoiceSmall')"
```

### **依赖问题**
```bash
# 重新安装依赖
pip install --upgrade funasr modelscope
```

### **GPU 支持**
```bash
# 安装 GPU 版本 PyTorch
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 📈 **性能指标**

- **CPU 推理**: 实时率 < 0.3
- **GPU 推理**: 实时率 < 0.1  
- **内存占用**: < 2GB
- **启动时间**: < 10s
- **中文准确率**: > 95%
- **英文准确率**: > 90%

## 🎯 **支持的音频格式**

- WAV (推荐)
- MP3
- M4A
- FLAC
- 其他 librosa 支持的格式

## 📝 **注意事项**

1. 首次运行会自动下载模型 (~2GB)
2. 推荐使用 16kHz 单声道音频
3. GPU 加速需要 CUDA 支持
4. 长音频会自动分段处理
