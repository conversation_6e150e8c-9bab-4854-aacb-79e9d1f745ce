#!/usr/bin/env python3
"""
SenseVoiceSmall 音频预处理模块
处理音频格式转换、采样率转换、VAD 语音活动检测等
"""

import numpy as np
import librosa
import soundfile as sf
import tempfile
import logging
from pathlib import Path
from typing import Optional, Tuple, List
import wave

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessor:
    """
    音频预处理器
    为 SenseVoiceSmall 提供标准化的音频预处理功能
    """
    
    def __init__(
        self,
        target_sample_rate: int = 16000,
        target_channels: int = 1,
        chunk_duration: float = 30.0,  # 30秒分段
        vad_enabled: bool = True
    ):
        """
        初始化音频处理器
        
        Args:
            target_sample_rate: 目标采样率
            target_channels: 目标声道数
            chunk_duration: 音频分段长度（秒）
            vad_enabled: 是否启用 VAD
        """
        self.target_sample_rate = target_sample_rate
        self.target_channels = target_channels
        self.chunk_duration = chunk_duration
        self.vad_enabled = vad_enabled
        
        logger.info(f"音频处理器初始化:")
        logger.info(f"  目标采样率: {target_sample_rate} Hz")
        logger.info(f"  目标声道数: {target_channels}")
        logger.info(f"  分段长度: {chunk_duration} 秒")
        logger.info(f"  VAD 启用: {vad_enabled}")
    
    def load_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """
        加载音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            (audio_data, sample_rate): 音频数据和采样率
        """
        try:
            # 使用 librosa 加载音频，自动转换为目标采样率和单声道
            audio, sr = librosa.load(
                audio_path,
                sr=self.target_sample_rate,
                mono=(self.target_channels == 1)
            )
            
            logger.debug(f"加载音频: {audio_path}")
            logger.debug(f"  原始采样率: {sr} Hz")
            logger.debug(f"  音频长度: {len(audio)} 样本 ({len(audio)/sr:.2f}s)")
            
            return audio, sr
            
        except Exception as e:
            logger.error(f"加载音频失败: {e}")
            raise
    
    def convert_raw_audio(
        self, 
        raw_audio: bytes, 
        original_sample_rate: int = 16000,
        sample_width: int = 2
    ) -> np.ndarray:
        """
        转换原始音频数据
        
        Args:
            raw_audio: 原始音频字节数据
            original_sample_rate: 原始采样率
            sample_width: 样本宽度（字节）
            
        Returns:
            处理后的音频数组
        """
        try:
            # 根据样本宽度确定数据类型
            if sample_width == 2:
                dtype = np.int16
            elif sample_width == 4:
                dtype = np.int32
            else:
                raise ValueError(f"不支持的样本宽度: {sample_width}")
            
            # 转换为 numpy 数组
            audio = np.frombuffer(raw_audio, dtype=dtype)
            
            # 转换为浮点数并归一化
            if dtype == np.int16:
                audio = audio.astype(np.float32) / 32768.0
            elif dtype == np.int32:
                audio = audio.astype(np.float32) / 2147483648.0
            
            # 重采样到目标采样率
            if original_sample_rate != self.target_sample_rate:
                audio = librosa.resample(
                    audio,
                    orig_sr=original_sample_rate,
                    target_sr=self.target_sample_rate
                )
            
            return audio
            
        except Exception as e:
            logger.error(f"转换原始音频失败: {e}")
            raise
    
    def save_audio_to_wav(
        self, 
        audio: np.ndarray, 
        output_path: str,
        sample_rate: Optional[int] = None
    ) -> str:
        """
        保存音频为 WAV 文件
        
        Args:
            audio: 音频数据
            output_path: 输出路径
            sample_rate: 采样率
            
        Returns:
            输出文件路径
        """
        if sample_rate is None:
            sample_rate = self.target_sample_rate
        
        try:
            # 确保音频数据在合理范围内
            audio = np.clip(audio, -1.0, 1.0)
            
            # 保存为 WAV 文件
            sf.write(output_path, audio, sample_rate)
            
            logger.debug(f"音频已保存: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"保存音频失败: {e}")
            raise
    
    def create_temp_wav(self, audio: np.ndarray) -> str:
        """
        创建临时 WAV 文件
        
        Args:
            audio: 音频数据
            
        Returns:
            临时文件路径
        """
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(
            suffix=".wav",
            delete=False
        )
        temp_path = temp_file.name
        temp_file.close()
        
        # 保存音频
        self.save_audio_to_wav(audio, temp_path)
        
        return temp_path
    
    def split_audio_by_duration(
        self, 
        audio: np.ndarray, 
        sample_rate: int
    ) -> List[np.ndarray]:
        """
        按时长分割音频
        
        Args:
            audio: 音频数据
            sample_rate: 采样率
            
        Returns:
            音频片段列表
        """
        chunk_samples = int(self.chunk_duration * sample_rate)
        chunks = []
        
        for i in range(0, len(audio), chunk_samples):
            chunk = audio[i:i + chunk_samples]
            if len(chunk) > 0:  # 确保不是空片段
                chunks.append(chunk)
        
        logger.debug(f"音频分割为 {len(chunks)} 个片段")
        return chunks
    
    def apply_vad(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        应用 VAD 语音活动检测
        
        Args:
            audio: 音频数据
            sample_rate: 采样率
            
        Returns:
            处理后的音频（移除静音段）
        """
        if not self.vad_enabled:
            return audio
        
        try:
            # 使用 librosa 的简单 VAD
            # 计算短时能量
            frame_length = int(0.025 * sample_rate)  # 25ms
            hop_length = int(0.010 * sample_rate)    # 10ms
            
            # 计算 RMS 能量
            rms = librosa.feature.rms(
                y=audio,
                frame_length=frame_length,
                hop_length=hop_length
            )[0]
            
            # 简单的阈值检测
            threshold = np.percentile(rms, 30)  # 使用30%分位数作为阈值
            voice_frames = rms > threshold
            
            # 扩展语音帧以避免切断
            voice_frames = np.convolve(voice_frames, np.ones(5), mode='same') > 0
            
            # 将帧索引转换为样本索引
            voice_samples = np.repeat(voice_frames, hop_length)
            voice_samples = voice_samples[:len(audio)]
            
            # 提取语音段
            if np.any(voice_samples):
                audio_vad = audio[voice_samples]
                logger.debug(f"VAD 处理: {len(audio)} -> {len(audio_vad)} 样本")
                return audio_vad
            else:
                logger.warning("VAD 未检测到语音活动")
                return audio
                
        except Exception as e:
            logger.warning(f"VAD 处理失败，使用原始音频: {e}")
            return audio
    
    def process_for_sensevoice(
        self, 
        audio_input,
        input_type: str = "file"
    ) -> str:
        """
        为 SenseVoice 处理音频
        
        Args:
            audio_input: 音频输入（文件路径或原始数据）
            input_type: 输入类型 ("file", "raw", "array")
            
        Returns:
            处理后的 WAV 文件路径
        """
        try:
            if input_type == "file":
                # 从文件加载
                audio, sr = self.load_audio(audio_input)
            elif input_type == "raw":
                # 从原始字节数据转换
                audio = self.convert_raw_audio(audio_input)
                sr = self.target_sample_rate
            elif input_type == "array":
                # 直接使用数组
                audio = audio_input
                sr = self.target_sample_rate
            else:
                raise ValueError(f"不支持的输入类型: {input_type}")
            
            # 应用 VAD（可选）
            if self.vad_enabled:
                audio = self.apply_vad(audio, sr)
            
            # 创建临时 WAV 文件
            temp_wav = self.create_temp_wav(audio)
            
            logger.debug(f"音频预处理完成: {temp_wav}")
            return temp_wav
            
        except Exception as e:
            logger.error(f"音频预处理失败: {e}")
            raise


def test_audio_processor():
    """测试音频处理器"""
    print("=== 测试音频处理器 ===")
    
    # 创建处理器
    processor = AudioProcessor()
    
    # 生成测试音频
    duration = 3.0
    sample_rate = 16000
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 生成包含语音和静音的测试信号
    audio = np.concatenate([
        np.zeros(int(0.5 * sample_rate)),  # 0.5秒静音
        0.3 * np.sin(2 * np.pi * 440 * t[:int(1.0 * sample_rate)]),  # 1秒语音
        np.zeros(int(0.5 * sample_rate)),  # 0.5秒静音
        0.3 * np.sin(2 * np.pi * 880 * t[:int(1.0 * sample_rate)]),  # 1秒语音
    ])
    
    print(f"测试音频: {len(audio)} 样本, {len(audio)/sample_rate:.2f}s")
    
    # 测试处理
    temp_wav = processor.process_for_sensevoice(audio, input_type="array")
    print(f"处理结果: {temp_wav}")
    
    # 验证输出文件
    if Path(temp_wav).exists():
        with wave.open(temp_wav, 'rb') as wav_file:
            print(f"输出文件:")
            print(f"  采样率: {wav_file.getframerate()} Hz")
            print(f"  声道数: {wav_file.getnchannels()}")
            print(f"  样本宽度: {wav_file.getsampwidth()} bytes")
            print(f"  帧数: {wav_file.getnframes()}")
        
        # 清理临时文件
        Path(temp_wav).unlink()
        print("✅ 音频处理器测试通过!")
    else:
        print("❌ 输出文件不存在")


if __name__ == "__main__":
    test_audio_processor()
