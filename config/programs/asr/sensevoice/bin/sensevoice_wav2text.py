#!/usr/bin/env python3
"""
SenseVoiceSmall ASR 程序
符合 Rhasspy 3.0 程序标准，从 WAV 文件输入，输出 JSON 格式的识别结果
"""

import argparse
import json
import logging
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from sensevoice_asr import SenseVoiceASR

_LOGGER = logging.getLogger("sensevoice_wav2text")

def main() -> None:
    """主函数"""
    parser = argparse.ArgumentParser(description="SenseVoiceSmall ASR 语音识别")
    parser.add_argument(
        "wav_file",
        help="输入的 WAV 音频文件"
    )
    parser.add_argument(
        "--model",
        default="models/asr/SenseVoiceSmall",
        help="SenseVoice 模型路径 (默认: models/asr/SenseVoiceSmall)"
    )
    parser.add_argument(
        "--language",
        default="auto",
        help="识别语言 (默认: auto)"
    )
    parser.add_argument(
        "--device",
        default="cpu",
        choices=["cpu", "cuda"],
        help="计算设备 (默认: cpu)"
    )
    parser.add_argument(
        "--use-vad",
        action="store_true",
        default=True,
        help="使用 VAD 语音活动检测"
    )
    parser.add_argument(
        "--use-punc",
        action="store_true", 
        default=True,
        help="使用标点预测"
    )
    parser.add_argument(
        "--use-itn",
        action="store_true",
        default=True,
        help="使用逆文本归一化"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试日志"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 检查输入文件
    wav_file = Path(args.wav_file)
    if not wav_file.exists():
        _LOGGER.error(f"音频文件不存在: {wav_file}")
        sys.exit(1)
    
    try:
        # 创建 ASR 服务
        asr = SenseVoiceASR(
            model_id=args.model,
            device=args.device,
            language=args.language,
            use_vad=args.use_vad,
            use_punc=args.use_punc,
            use_itn=args.use_itn
        )
        
        _LOGGER.info(f"开始识别音频文件: {wav_file}")
        
        # 执行语音识别
        result = asr.recognize_file(str(wav_file))
        
        # 输出 Rhasspy 3.0 标准格式的 JSON 结果
        output = {
            "text": result.get("text", ""),
            "language": result.get("language", args.language),
            "model": "SenseVoiceSmall",
            "processing_time": result.get("processing_time", 0),
            "audio_duration": result.get("audio_duration", 0),
            "real_time_factor": result.get("real_time_factor", 0)
        }
        
        # 如果有错误，添加错误信息
        if "error" in result:
            output["error"] = result["error"]
        
        # 输出 JSON 结果到标准输出
        print(json.dumps(output, ensure_ascii=False, indent=2))
        
        if result.get("text"):
            _LOGGER.info(f"识别成功: '{result['text']}'")
        else:
            _LOGGER.warning("识别结果为空")
    
    except Exception as e:
        _LOGGER.error(f"ASR 识别失败: {e}")
        
        # 输出错误结果
        error_output = {
            "text": "",
            "language": args.language,
            "model": "SenseVoiceSmall",
            "error": str(e)
        }
        print(json.dumps(error_output, ensure_ascii=False, indent=2))
        sys.exit(1)


if __name__ == "__main__":
    main()
