#!/usr/bin/env python3
"""
简化的 Dify 意图理解程序
直接调用 Dify API，从命令行参数获取配置
"""

import sys
import os
import json
import argparse
import logging
from pathlib import Path

# 添加父目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from dify_client import DifyClient, DifyConfig, DifyAPIError

# 禁用日志输出，确保 stdout 只有 JSON
logger = logging.getLogger(__name__)
logger.setLevel(logging.CRITICAL)  # 只输出严重错误
logger.propagate = False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="简化的 Dify 意图理解程序")

    # 基本参数
    parser.add_argument("text", help="要分析的文本")
    parser.add_argument("--output", "-o", help="输出文件路径")

    # Dify API 配置参数
    parser.add_argument("--base-url", default="https://api.dify.ai",
                       help="Dify API 基础 URL")
    parser.add_argument("--api-key", required=True,
                       help="Dify API 密钥")

    # 处理参数
    parser.add_argument("--user", default="rhasspy-user",
                       help="用户标识")
    parser.add_argument("--conversation-id",
                       help="对话ID（用于多轮对话）")
    parser.add_argument("--timeout", type=int, default=30, help="请求超时")

    # 输出参数
    parser.add_argument("--format", choices=["json", "text"], default="json",
                       help="输出格式")

    # 调试参数
    parser.add_argument("--debug", action="store_true", help="调试模式")

    return parser.parse_args()

def call_dify_api(client: DifyClient, text: str, user: str, conversation_id: str = None) -> dict:
    """调用 Dify API"""
    try:
        logger.info(f"调用 Dify API: {text}")
        
        response = client.chat(
            query=text,
            user=user,
            conversation_id=conversation_id
        )
        
        if not response:
            return {
                'success': False,
                'error': 'Dify API 响应为空'
            }
        
        # 提取响应内容
        answer = response.get('answer', '')
        conversation_id = response.get('conversation_id', '')
        
        return {
            'success': True,
            'response': answer,
            'conversation_id': conversation_id,
            'original_text': text,
            'dify_response': response
        }
        
    except DifyAPIError as e:
        logger.error(f"Dify API 错误: {e}")
        return {
            'success': False,
            'error': f'Dify API 错误: {str(e)}',
            'status_code': e.status_code
        }
    except Exception as e:
        logger.error(f"调用 Dify API 失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def format_output(result: dict, format_type: str) -> str:
    """格式化输出为 Rhasspy 3.0 事件格式"""
    if format_type == "json":
        if result.get('success'):
            # 成功时返回 intent 事件格式
            event = {
                "type": "intent",
                "data": {
                    "name": "dify_response",
                    "text": result.get('response', ''),
                    "conversation_id": result.get('conversation_id', ''),
                    "original_text": result.get('original_text', '')
                }
            }
        else:
            # 失败时返回 not-recognized 事件格式
            event = {
                "type": "not-recognized",
                "data": {
                    "text": result.get('original_text', ''),
                    "error": result.get('error', '未知错误')
                }
            }
        return json.dumps(event, ensure_ascii=False)
    else:
        # 文本格式
        if result.get('success'):
            response = result.get('response', '无响应')
            return response
        else:
            return f"错误: {result.get('error', '未知错误')}"

def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 检查 API 密钥
        if not args.api_key or args.api_key.startswith('app-your-'):
            logger.error("Dify API 密钥未配置或无效")
            logger.error("请在 configuration.yaml 中设置正确的 API 密钥")

            error_result = {
                'success': False,
                'error': 'Dify API 密钥未配置，请在 configuration.yaml 中设置正确的 API 密钥'
            }

            output = format_output(error_result, args.format)

            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(output)
            else:
                print(output)

            return 1

        # 初始化 Dify 客户端
        dify_config = DifyConfig(
            base_url=args.base_url,
            api_key=args.api_key,
            timeout=args.timeout
        )
        client = DifyClient(dify_config)
        
        # 调用 Dify API
        result = call_dify_api(client, args.text, args.user, args.conversation_id)

        # 格式化输出
        output = format_output(result, args.format)

        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output)
        else:
            print(output)
        
        return 0 if result.get('success') else 1
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        
        error_result = {
            'success': False,
            'error': str(e)
        }
        
        output = format_output(error_result, args.format)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output)
        else:
            print(output)
        
        return 1

if __name__ == "__main__":
    exit(main())
