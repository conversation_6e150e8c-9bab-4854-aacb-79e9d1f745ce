#!/bin/bash
# 简化的 Dify 客户端安装脚本

set -e

echo "🤖 开始安装简化的 Dify 客户端..."

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROGRAM_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(cd "$PROGRAM_DIR/../../../.." && pwd)"

echo "程序目录: $PROGRAM_DIR"
echo "项目根目录: $PROJECT_ROOT"

# 检查 conda 环境
if ! conda info --envs | grep -q "aibi"; then
    echo "❌ 未找到 aibi conda 环境"
    echo "请先创建 aibi 环境: conda create -n aibi python=3.10"
    exit 1
fi

echo "✅ 找到 aibi conda 环境"

# 激活环境并安装依赖
echo "📦 安装 Python 依赖..."
conda run -n aibi pip install -r "$PROGRAM_DIR/requirements.txt"

# 检查主配置文件
CONFIG_FILE="$PROJECT_ROOT/config/configuration.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 主配置文件不存在: $CONFIG_FILE"
    echo "请确保 Rhasspy 配置文件存在"
    exit 1
else
    echo "✅ 主配置文件存在: $CONFIG_FILE"
fi

# 设置可执行权限
echo "🔧 设置可执行权限..."
chmod +x "$PROGRAM_DIR/bin/dify_intent.py"

# 注意：不再需要 handle 程序，Dify 工作流直接返回响应

# 测试安装
echo "🧪 测试安装..."
cd "$PROGRAM_DIR"

# 测试程序参数
echo "测试程序参数..."
if conda run -n aibi python bin/dify_intent.py --help > /dev/null 2>&1; then
    echo "✅ 程序参数正常"
else
    echo "❌ 程序参数测试失败"
    exit 1
fi

# 测试 Dify 客户端
echo "测试 Dify 客户端..."
if conda run -n aibi python dify_client.py > /dev/null 2>&1; then
    echo "✅ Dify 客户端模块正常"
else
    echo "❌ Dify 客户端模块测试失败"
    exit 1
fi

# 测试主程序帮助
echo "测试主程序..."
if conda run -n aibi python bin/dify_intent.py --help > /dev/null 2>&1; then
    echo "✅ 意图理解程序正常"
else
    echo "❌ 意图理解程序测试失败"
    exit 1
fi

# 不再需要 handle 程序测试

echo ""
echo "🎉 简化的 Dify 客户端安装完成!"
echo ""
echo "📋 使用说明:"
echo "  1. 配置 Dify API 密钥: 编辑 $CONFIG_FILE 中的 api_key"
echo "  2. 测试客户端: python bin/dify_intent.py \"你好\" --api-key \"your-key\" --format text"
echo "  3. 查看帮助: python bin/dify_intent.py --help"
echo ""
echo "🔧 配置 Rhasspy:"
echo "  在 configuration.yaml 的 intent.dify.template_args 中设置 api_key"
echo ""
echo "⚠️  注意事项:"
echo "  - 请在 configuration.yaml 中设置正确的 Dify API 密钥"
echo "  - 所有智能逻辑都在 Dify 工作流中实现"
echo "  - 本程序只负责与 Dify API 通信"
echo "  - 配置统一在 configuration.yaml 中管理"
echo ""
