applications:
  chat:
    api_key: app-your-chat-api-key
    description: 日常对话和问答
    enabled: true
    name: 闲聊对话
    type: chatflow
  nlu:
    api_key: app-your-nlu-api-key
    description: 识别用户意图和提取实体
    enabled: true
    name: 意图理解
    type: chatflow
  smart_home:
    api_key: app-your-smart-home-api-key
    description: 控制智能家居设备
    enabled: true
    name: 智能家居控制
    type: workflow
dify:
  base_url: https://api.dify.ai
  retry_count: 3
  retry_delay: 1.0
  timeout: 30
smart_home:
  devices:
    bedroom_ac:
      api_endpoint: http://*************/api/ac
      capabilities:
      - on_off
      - temperature
      - mode
      enabled: true
      name: 卧室空调
      room: 卧室
      type: air_conditioner
    living_room_curtain:
      api_endpoint: http://*************/api/curtain
      capabilities:
      - on_off
      - position
      enabled: true
      name: 客厅窗帘
      room: 客厅
      type: curtain
    living_room_light:
      api_endpoint: http://*************/api/light
      capabilities:
      - on_off
      - brightness
      - color
      enabled: true
      name: 客厅灯
      room: 客厅
      type: light
  scenes:
    away_mode:
      description: 离家时的场景设置
      devices:
      - action: turn_off
        device_id: living_room_light
      - action: turn_off
        device_id: bedroom_ac
      - action: close
        device_id: living_room_curtain
      enabled: true
      name: 离家模式
    home_mode:
      description: 回家时的场景设置
      devices:
      - action: turn_on
        brightness: 80
        device_id: living_room_light
      - action: turn_on
        device_id: bedroom_ac
        temperature: 26
      - action: open
        device_id: living_room_curtain
        position: 100
      enabled: true
      name: 回家模式
    sleep_mode:
      description: 睡觉时的场景设置
      devices:
      - action: turn_off
        device_id: living_room_light
      - action: turn_on
        device_id: bedroom_ac
        temperature: 24
      - action: close
        device_id: living_room_curtain
      enabled: true
      name: 睡眠模式
