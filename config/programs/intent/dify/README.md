# 🤖 简化的 Dify 客户端

简化的 Dify API 客户端，专为 Rhasspy 3.0 语音助手设计。

## 📋 **设计原则**

- **纯客户端**: 只负责与 Dify API 通信，不做任何业务逻辑
- **配置统一**: 所有配置都在 `configuration.yaml` 中管理
- **简单直接**: 直接从命令行参数获取配置，无需额外配置文件
- **工作流驱动**: 所有智能逻辑都在 Dify 工作流中实现

## 🏗️ **架构设计**

```
用户语音 → ASR → 文本 → Dify API → Dify 工作流 → 响应文本 → TTS → 语音输出
```

### **工作流程**
1. 用户说话被 ASR 转换为文本
2. 文本发送给 Dify API
3. Dify 工作流处理请求（意图识别、智能家居控制、对话生成等）
4. Dify 返回最终响应文本
5. 响应文本通过 TTS 转换为语音输出

## 🔧 **配置说明**

### **在 configuration.yaml 中配置**

```yaml
programs:
  intent:
    dify:
      command: |
        conda run -n aibi python config/programs/intent/dify/bin/dify_intent.py "${text}" --base-url "${base_url}" --api-key "${api_key}" --user "${user}" --timeout "${timeout}" --format json
      template_args:
        base_url: "https://api.dify.ai"          # Dify API 地址
        api_key: "app-your-api-key-here"         # 您的 Dify API 密钥
        user: "rhasspy-user"                     # 用户标识
        timeout: "30"                            # 请求超时时间

pipelines:
  default:
    intent:
      name: dify
    handle:
      name: dify-extract
```

### **配置步骤**
1. 在 Dify 平台创建应用并获取 API 密钥
2. 在 `configuration.yaml` 中将 `api_key` 替换为您的实际密钥
3. 如果使用自部署的 Dify，修改 `base_url`

## 🚀 **使用方法**

### **命令行使用**
```bash
# 基本用法
python bin/dify_intent.py "你好" --api-key "app-xxx" --format text

# 完整参数
python bin/dify_intent.py "打开客厅的灯" \
  --base-url "https://api.dify.ai" \
  --api-key "app-xxx" \
  --user "test-user" \
  --timeout 30 \
  --format json \
  --output result.json
```

### **参数说明**
- `text`: 要处理的文本（必需）
- `--api-key`: Dify API 密钥（必需）
- `--base-url`: Dify API 地址（默认: https://api.dify.ai）
- `--user`: 用户标识（默认: rhasspy-user）
- `--timeout`: 请求超时时间（默认: 30秒）
- `--format`: 输出格式，json 或 text（默认: json）
- `--output`: 输出文件路径（可选）
- `--conversation-id`: 对话ID，用于多轮对话（可选）
- `--debug`: 调试模式（可选）

## 📁 **文件结构**

```
config/programs/intent/dify/
├── bin/
│   └── dify_intent.py          # 主程序
├── dify_client.py              # Dify API 客户端
├── requirements.txt            # 依赖包
├── script/setup                # 安装脚本
└── README.md                   # 本文档
```

## 🧪 **测试**

```bash
# 运行测试
python tests/dify/test_dify_simple.py

# 或通过主测试脚本
python tests/main_test.py --modules dify
```

## 🔍 **故障排除**

### **常见问题**

1. **API 密钥错误**
   ```
   错误: Dify API 错误: Access token is invalid
   ```
   解决: 检查 `configuration.yaml` 中的 `api_key` 是否正确

2. **网络连接问题**
   ```
   错误: 请求失败: Connection timeout
   ```
   解决: 检查网络连接和 `base_url` 配置

3. **参数缺失**
   ```
   error: the following arguments are required: --api-key
   ```
   解决: 确保在 `configuration.yaml` 中正确配置了 `template_args`

### **调试模式**
```bash
python bin/dify_intent.py "测试" --api-key "app-xxx" --debug
```

## 🎯 **Dify 工作流建议**

在 Dify 平台中，建议创建以下工作流：

### **1. 智能家居控制工作流**
- 意图识别节点：识别设备控制、场景切换等意图
- 参数提取节点：提取设备名称、动作、参数等
- HTTP 请求节点：调用智能家居设备 API
- 响应生成节点：生成确认回复

### **2. 信息查询工作流**
- 查询类型识别：天气、新闻、知识问答等
- 外部 API 调用：获取实时信息
- 结果格式化：生成用户友好的回复

### **3. 闲聊对话工作流**
- LLM 节点：使用大语言模型进行对话
- 上下文管理：维护对话历史
- 个性化回复：根据用户偏好调整回复风格

## 📝 **开发说明**

### **扩展功能**
如需扩展功能，建议：
1. 在 Dify 工作流中添加新的处理逻辑
2. 保持客户端代码简单，只负责 API 通信
3. 通过 Dify 的节点系统实现复杂业务逻辑

### **性能优化**
- 使用 Dify 的缓存功能减少重复计算
- 合理设置超时时间
- 考虑使用 Dify 的流式响应（如需要）

## 🎉 **总结**

这个简化的 Dify 客户端遵循"单一职责"原则，只负责与 Dify API 的通信。所有的智能逻辑都在 Dify 工作流中实现，使得系统更加清晰、易于维护和扩展。

通过统一的配置管理和标准化的接口，它完美集成到 Rhasspy 3.0 语音助手系统中，为用户提供强大的 AI 能力。
