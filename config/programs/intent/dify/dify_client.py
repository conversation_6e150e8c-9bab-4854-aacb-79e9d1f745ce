#!/usr/bin/env python3
"""
简化的 Dify API 客户端
只负责与 Dify 平台的基础 HTTP 通信
"""

import json
import sys
import time
import logging
import requests
from typing import Dict, Optional
from dataclasses import dataclass

# 禁用日志输出
logger = logging.getLogger(__name__)
logger.setLevel(logging.CRITICAL)
logger.propagate = False

@dataclass
class DifyConfig:
    """Dify 配置"""
    base_url: str = "https://api.dify.ai"
    api_key: str = ""
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0

class DifyAPIError(Exception):
    """Dify API 错误"""
    def __init__(self, message: str, status_code: int = None, response: Dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response = response

class DifyClient:
    """简化的 Dify API 客户端"""
    
    def __init__(self, config: DifyConfig):
        """
        初始化 Dify 客户端
        
        Args:
            config: Dify 配置
        """
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Aibi-Rhasspy/1.0'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        发送 HTTP 请求
        
        Args:
            method: HTTP 方法
            endpoint: API 端点
            **kwargs: 请求参数
            
        Returns:
            响应对象
            
        Raises:
            DifyAPIError: API 请求失败
        """
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        # 设置超时
        kwargs.setdefault('timeout', self.config.timeout)
        
        for attempt in range(self.config.retry_count):
            try:
                logger.debug(f"请求 {method} {url}, 尝试 {attempt + 1}/{self.config.retry_count}")
                
                response = self.session.request(method, url, **kwargs)
                
                # 检查响应状态
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:  # 限流
                    if attempt < self.config.retry_count - 1:
                        wait_time = self.config.retry_delay * (2 ** attempt)
                        logger.warning(f"API 限流，等待 {wait_time}s 后重试")
                        time.sleep(wait_time)
                        continue
                
                # 解析错误响应
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', f'HTTP {response.status_code}')
                except:
                    error_message = f'HTTP {response.status_code}: {response.text}'
                
                raise DifyAPIError(
                    message=error_message,
                    status_code=response.status_code,
                    response=error_data if 'error_data' in locals() else None
                )
                
            except requests.exceptions.RequestException as e:
                if attempt < self.config.retry_count - 1:
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.warning(f"请求异常: {e}, 等待 {wait_time}s 后重试")
                    time.sleep(wait_time)
                    continue
                else:
                    raise DifyAPIError(f"请求失败: {e}")
        
        raise DifyAPIError("达到最大重试次数")
    
    def chat(self, query: str, user: str = "default", 
             conversation_id: str = None, inputs: Dict = None) -> Dict:
        """
        发送聊天消息
        
        Args:
            query: 用户查询
            user: 用户标识
            conversation_id: 对话ID
            inputs: 输入参数
            
        Returns:
            API 响应
        """
        data = {
            'query': query,
            'inputs': inputs or {},
            'user': user,
            'response_mode': 'blocking',
            'auto_generate_name': True
        }
        
        if conversation_id:
            data['conversation_id'] = conversation_id
        
        logger.info(f"发送聊天消息: {query}")
        
        response = self._make_request('POST', '/v1/chat-messages', json=data)
        return response.json()
    
    def get_application_info(self) -> Dict:
        """
        获取应用信息
        
        Returns:
            应用信息
        """
        response = self._make_request('GET', '/v1/info')
        return response.json()
    
    def get_conversations(self, user: str = "default", limit: int = 20) -> Dict:
        """
        获取对话列表
        
        Args:
            user: 用户标识
            limit: 返回数量限制
            
        Returns:
            对话列表
        """
        params = {'user': user, 'limit': limit}
        response = self._make_request('GET', '/v1/conversations', params=params)
        return response.json()

def main():
    """测试函数"""
    # 配置示例
    config = DifyConfig(
        base_url="https://api.dify.ai",
        api_key="app-your-api-key-here",
        timeout=30
    )
    
    client = DifyClient(config)
    
    try:
        # 测试应用信息
        info = client.get_application_info()
        print(f"应用信息: {info}")
        
        # 测试聊天
        response = client.chat(
            query="你好，请介绍一下你自己",
            user="test-user"
        )
        print(f"聊天响应: {response}")
        
    except DifyAPIError as e:
        print(f"API 错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    main()
