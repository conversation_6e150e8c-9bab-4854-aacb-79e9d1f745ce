#!/usr/bin/env bash
# Hey, 艾比 唤醒词检测程序安装脚本（基于 Conda 环境）

set -e

# 获取脚本目录
this_dir="$( cd "$( dirname "$0" )" && pwd )"
base_dir="$(realpath "${this_dir}/..")"

echo "🚀 安装 Hey, 艾比 唤醒词检测程序（使用 Conda 环境）..."

# 检查 conda 是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ Conda 未安装或未在 PATH 中"
    echo "💡 请确保已安装 Miniconda/Anaconda 并激活了环境"
    exit 1
fi

# 检查是否在 conda 环境中
if [ -z "$CONDA_DEFAULT_ENV" ]; then
    echo "❌ 未检测到活跃的 Conda 环境"
    echo "💡 请先激活 Conda 环境: conda activate aibi"
    exit 1
fi

echo "📋 当前 Conda 环境: $CONDA_DEFAULT_ENV"

# 检查 Python 版本
python_version=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "📋 Python 版本: $python_version"

major_version=$(python -c "import sys; print(sys.version_info.major)")
minor_version=$(python -c "import sys; print(sys.version_info.minor)")

if [ "$major_version" -lt 3 ] || ([ "$major_version" -eq 3 ] && [ "$minor_version" -lt 8 ]); then
    echo "❌ Python 版本过低 ($python_version)，需要 3.8+"
    exit 1
fi

# 升级 pip
echo "📦 升级 pip..."
pip install --upgrade pip

# 安装依赖到当前 conda 环境
echo "📦 安装依赖到 Conda 环境..."
pip install -r "${base_dir}/requirements.txt"

# 设置可执行权限
echo "🔐 设置可执行权限..."
chmod +x "${base_dir}/bin/hey_aibi_raw_text.py"

# 检查模型文件
model_file="${base_dir}/share/hey_aibi.onnx"
if [ ! -f "$model_file" ]; then
    echo "⚠️  模型文件不存在: $model_file"
    echo "请将 ONNX 模型文件复制到 share/ 目录下"
    echo "例如: cp /path/to/your/hey_aibi.onnx ${base_dir}/share/"
else
    echo "✅ 模型文件已找到: $model_file"
fi

echo "🎉 Hey, 艾比 唤醒词检测程序安装完成！"
echo ""
echo "📋 使用方法:"
echo "  1. 确保激活环境: conda activate $CONDA_DEFAULT_ENV"
echo "  2. 确保模型文件在 share/hey_aibi.onnx"
echo "  3. 运行: python bin/hey_aibi_raw_text.py --model share/hey_aibi.onnx"
echo "  4. 或在 Rhasspy 3.0 配置中使用"
echo ""
echo "🔧 测试命令:"
echo "  echo 'test' | python bin/hey_aibi_raw_text.py --debug"
