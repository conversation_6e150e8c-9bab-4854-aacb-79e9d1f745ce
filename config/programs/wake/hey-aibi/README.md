# Hey, 艾比 唤醒词检测程序

基于自训练 TC-ResNet8 模型的唤醒词检测程序，专为 Rhasspy 3.0 设计。

## 📋 **功能特点**

- 🎯 **高精度检测**: 基于 TC-ResNet8 深度学习模型
- ⚡ **实时处理**: 支持流式音频处理，低延迟响应
- 🔧 **易于集成**: 符合 Rhasspy 3.0 程序标准
- 📊 **可配置**: 支持阈值、模型路径等参数配置
- 🎵 **标准输入**: 接收 16kHz 16-bit 单声道 PCM 音频

## 🚀 **安装使用**

### **安装依赖**
```bash
# 运行安装脚本
./script/setup

# 或手动安装
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

### **准备模型**
```bash
# 将 ONNX 模型文件复制到 share 目录
cp /path/to/your/hey_aibi.onnx share/
```

### **运行程序**
```bash
# 基本用法
.venv/bin/python3 bin/hey_aibi_raw_text.py --model share/hey_aibi.onnx

# 调试模式
.venv/bin/python3 bin/hey_aibi_raw_text.py --debug --threshold 0.8

# 从音频文件测试
arecord -r 16000 -c 1 -f S16_LE -t raw | .venv/bin/python3 bin/hey_aibi_raw_text.py
```

## ⚙️ **配置参数**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--model` | `share/hey_aibi.onnx` | ONNX 模型文件路径 |
| `--threshold` | `0.85` | 检测阈值 (0-1) |
| `--samples-per-chunk` | `1024` | 每次处理的样本数 |
| `--wake-word` | `hey_aibi` | 唤醒词名称 |
| `--overlap-ratio` | `0.5` | 音频窗口重叠比例 |
| `--debug` | `False` | 启用调试日志 |

## 🔧 **Rhasspy 3.0 集成**

在 `configuration.yaml` 中添加：

```yaml
programs:
  wake:
    hey-aibi:
      command: |
        .venv/bin/python3 bin/hey_aibi_raw_text.py --model "${model}" --threshold ${threshold} --wake-word "${wake_word}"
      adapter: |
        wake_adapter_raw.py
      template_args:
        model: "share/hey_aibi.onnx"
        threshold: 0.85
        wake_word: "hey_aibi"
```

## 📊 **模型要求**

- **输入格式**: 16kHz 单声道 PCM 音频
- **特征提取**: 40维 MFCC (窗长25ms, 帧移10ms)
- **模型输入**: [1, 1, 100, 40] (batch, channel, time, features)
- **模型输出**: [1, 1] 原始分数 (需 Sigmoid 激活)

## 🧪 **测试验证**

```bash
# 测试 MFCC 特征提取
python3 mfcc_extractor.py

# 测试唤醒词检测
python3 wake_word_detector.py

# 端到端测试
echo "test audio" | .venv/bin/python3 bin/hey_aibi_raw_text.py --debug
```

## 📝 **文件结构**

```
hey-aibi/
├── bin/
│   └── hey_aibi_raw_text.py    # 主程序
├── script/
│   └── setup                   # 安装脚本
├── share/
│   └── hey_aibi.onnx          # ONNX 模型文件
├── requirements.txt            # Python 依赖
├── mfcc_extractor.py          # MFCC 特征提取器
├── wake_word_detector.py      # 唤醒词检测器
└── README.md                  # 说明文档
```

## 🔍 **故障排除**

### **常见问题**

1. **模型文件不存在**
   ```bash
   # 检查模型文件路径
   ls -la share/hey_aibi.onnx
   ```

2. **依赖安装失败**
   ```bash
   # 升级 pip 并重新安装
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **音频格式错误**
   ```bash
   # 确保音频格式为 16kHz 16-bit 单声道
   arecord -r 16000 -c 1 -f S16_LE -t raw
   ```

### **调试技巧**

- 使用 `--debug` 参数查看详细日志
- 检查音频输入格式和采样率
- 验证模型文件完整性
- 调整检测阈值 `--threshold`

---

**🎯 准备就绪！** 现在可以在 Rhasspy 3.0 中使用 Hey, 艾比 唤醒词检测了。
