#!/usr/bin/env python3
"""
Hey, 艾比 唤醒词检测程序
符合 Rhasspy 3.0 程序标准，从标准输入读取 16kHz PCM 音频，检测到唤醒词时输出到标准输出
"""

import argparse
import logging
import sys
import numpy as np
from pathlib import Path

# 添加当前目录到 Python 路径，以便导入自定义模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from wake_word_detector import WakeWordDetector

_LOGGER = logging.getLogger("hey_aibi_raw_text")

def main() -> None:
    """主函数"""
    parser = argparse.ArgumentParser(description="Hey, 艾比 唤醒词检测")
    parser.add_argument(
        "--model",
        default="share/hey_aibi.onnx",
        help="ONNX 模型文件路径 (默认: share/hey_aibi.onnx)"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.85,
        help="检测阈值 (默认: 0.85)"
    )
    parser.add_argument(
        "--samples-per-chunk",
        type=int,
        default=1024,
        help="每次处理的样本数 (默认: 1024)"
    )
    parser.add_argument(
        "--wake-word",
        default="hey_aibi",
        help="唤醒词名称 (默认: hey_aibi)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试日志"
    )
    parser.add_argument(
        "--overlap-ratio",
        type=float,
        default=0.5,
        help="音频窗口重叠比例 (默认: 0.5)"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 解析模型路径（支持相对路径）
    model_path = Path(args.model)
    if not model_path.is_absolute():
        # 相对于程序目录的路径
        program_dir = Path(__file__).parent.parent
        model_path = program_dir / model_path

    # 解析绝对路径
    model_path = model_path.resolve()
    
    if not model_path.exists():
        _LOGGER.error(f"模型文件不存在: {model_path}")
        sys.exit(1)
    
    try:
        # 创建唤醒词检测器
        detector = WakeWordDetector(
            model_path=str(model_path),
            threshold=args.threshold,
            sample_rate=16000,
            window_size=16000,
            overlap_ratio=args.overlap_ratio
        )
        
        _LOGGER.info(f"开始监听唤醒词: {args.wake_word}")
        _LOGGER.info(f"每次处理 {args.samples_per_chunk} 样本")
        
        # 从标准输入读取 16kHz, 16-bit 单声道 PCM 音频
        bytes_per_chunk = args.samples_per_chunk * 2  # 16-bit = 2 bytes
        
        while True:
            # 读取音频数据
            audio_bytes = sys.stdin.buffer.read(bytes_per_chunk)
            if not audio_bytes:
                _LOGGER.info("音频流结束")
                break
            
            # 如果读取的数据不足，跳过
            if len(audio_bytes) < bytes_per_chunk:
                continue
            
            # 转换为 numpy 数组 (16-bit PCM -> float32)
            audio_chunk = np.frombuffer(audio_bytes, dtype=np.int16).astype(np.float32)
            
            # 归一化到 [-1, 1] 范围
            audio_chunk = audio_chunk / 32768.0
            
            # 处理音频块
            is_detected, confidence = detector.process_audio_chunk(audio_chunk)
            
            if is_detected:
                # 检测到唤醒词，输出到标准输出
                print(args.wake_word, flush=True)
                confidence_ratio = confidence / args.threshold
                _LOGGER.info(f"🎉 已检测到唤醒词！置信度为：{confidence:.4f}/{args.threshold} ({confidence_ratio:.2f}x)")

                # 可选：重置检测器状态以避免重复检测
                # detector.reset()
            else:
                # 显示当前置信度（调试模式）
                if args.debug and confidence > 0.1:  # 只显示有意义的置信度
                    confidence_ratio = confidence / args.threshold
                    _LOGGER.debug(f"当前置信度：{confidence:.4f}/{args.threshold} ({confidence_ratio:.2f}x)")
    
    except KeyboardInterrupt:
        _LOGGER.info("检测器已停止")
    except Exception as e:
        _LOGGER.error(f"检测器错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
