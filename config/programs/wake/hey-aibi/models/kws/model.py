import torch
import torch.nn as nn
from typing import Union
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

# ===================================================================
#                      基本残差模块 (Residual Block)
# ===================================================================
class ResidualBlock(nn.Module):
    """
    TC-ResNet的基本组成单元：残差模块。
    包含两个卷积层、批归一化、ReLU激活函数，并带有残差连接。
    """
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding: Union[str, int] = "same"):
        super(ResidualBlock, self).__init__()

        # 第一个卷积层
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()

        # 第二个卷积层 (步长固定为1，填充固定为'same'以保持尺寸)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=kernel_size, stride=1, padding="same")
        self.bn2 = nn.BatchNorm2d(out_channels)

        # 残差连接的下采样层 (如果输入输出通道数或步长不一致)
        self.downsample = nn.Sequential() # 默认为空
        if stride != 1 or in_channels != out_channels:
            self.downsample = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, padding=0),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        """前向传播"""
        identity = x # 保存输入，用于残差连接

        # 主路径
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        # 如果有下采样层，对输入进行下采样
        if self.downsample is not None:
            identity = self.downsample(x)

        # 残差连接
        out += identity
        out = self.relu(out)
        
        return out

# ===================================================================
#                      TC-ResNet8 主模型
# ===================================================================
class TCResNet8(nn.Module):
    """
    一个轻量级的8层时序卷积残差网络 (TC-ResNet8)。
    专为关键词识别（如唤醒词检测）设计。
    """
    def __init__(self, input_channels=1, num_classes=1, k: Union[int, float] = 1):
        super(TCResNet8, self).__init__()

        # 使用k参数来控制通道数
        c1, c2, c3 = int(16 * k), int(24 * k), int(32 * k)

        # 初始卷积层
        self.initial_conv = nn.Conv2d(input_channels, c1, kernel_size=3, stride=1, padding="same")
        self.initial_bn = nn.BatchNorm2d(c1)
        self.initial_relu = nn.ReLU()

        # 堆叠的残差模块
        self.res_block1 = ResidualBlock(c1, c1, kernel_size=3, stride=2, padding=1) # 降低时间维度
        self.res_block2 = ResidualBlock(c1, c1, kernel_size=3, stride=1, padding="same")
        self.res_block3 = ResidualBlock(c1, c2, kernel_size=3, stride=2, padding=1) # 降低时间维度 & 增加通道
        self.res_block4 = ResidualBlock(c2, c2, kernel_size=3, stride=1, padding="same")
        self.res_block5 = ResidualBlock(c2, c3, kernel_size=3, stride=2, padding=1) # 降低时间维度 & 增加通道
        self.res_block6 = ResidualBlock(c3, c3, kernel_size=3, stride=1, padding="same")
        
        # 全局平均池化
        self.avg_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 全连接层 (分类器)
        self.fc = nn.Linear(c3, num_classes)

    def forward(self, x):
        """
        前向传播
        x 的预期 shape: [batch_size, channels, time_steps, n_mfcc]
                       例如: [64, 1, 100, 40]
        """
        # 初始卷积
        out = self.initial_conv(x)
        out = self.initial_bn(out)
        out = self.initial_relu(out)

        # 残差模块
        out = self.res_block1(out)
        out = self.res_block2(out)
        out = self.res_block3(out)
        out = self.res_block4(out)
        out = self.res_block5(out)
        out = self.res_block6(out)
        
        # 池化
        out = self.avg_pool(out)
        
        # 展平以用于全连接层
        out = torch.flatten(out, 1)
        
        # 分类
        out = self.fc(out)
        
        return out

if __name__ == '__main__':
    # ================== 模型测试代码 ==================
    # 这是一个简单的前向传播测试，确保模型的维度和计算没有问题
    
    # 模拟一个批次的数据
    # batch_size=64, channels=1, time_steps=100, n_mfcc=40
    dummy_input = torch.randn(64, 1, 100, 40) 
    
    # 初始化模型 (测试k=1的标准大小)
    model = TCResNet8(input_channels=1, num_classes=1, k=1)
    
    # 前向传播
    output = model(dummy_input)
    
    # 打印模型结构和输出维度
    logger.info("TC-ResNet8 Model Structure:")
    logger.info(str(model))
    logger.info(f"\nInput Shape: {dummy_input.shape}")
    logger.info(f"Output Shape: {output.shape}")  # 预期: [64, 1]

    # 检查输出维度是否正确
    assert output.shape == (64, 1), f"Output shape is {output.shape}, but expected (64, 1)"

    logger.info("\nModel forward pass test successful for k=1!")

    # 测试k=0.5的大小
    dummy_input_small = torch.randn(64, 1, 100, 40)
    model_small = TCResNet8(input_channels=1, num_classes=1, k=0.5)
    output_small = model_small(dummy_input_small)
    logger.info("\nTesting for k=0.5...")
    logger.info(f"Input Shape: {dummy_input_small.shape}")
    logger.info(f"Output Shape: {output_small.shape}")
    assert output_small.shape == (64, 1), f"Output shape is {output_small.shape}, but expected (64, 1)"
    logger.info("Model forward pass test successful for k=0.5!")

