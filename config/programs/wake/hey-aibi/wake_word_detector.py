#!/usr/bin/env python3
"""
Hey, 艾比 唤醒词检测器
基于 ONNX Runtime 的实时唤醒词检测，支持滑动窗口处理和阈值判断
"""

import numpy as np
import onnxruntime as ort
import logging
from typing import Optional, Tuple
from collections import deque
import time

from mfcc_extractor import MFCCExtractor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioBuffer:
    """
    音频缓冲区，实现滑动窗口处理
    """
    
    def __init__(self, window_size: int = 16000, overlap_ratio: float = 0.5):
        """
        初始化音频缓冲区
        
        Args:
            window_size: 窗口大小（样本数），默认 16000 (1秒)
            overlap_ratio: 重叠比例，默认 0.5 (50% 重叠)
        """
        self.window_size = window_size
        self.hop_size = int(window_size * (1 - overlap_ratio))
        self.buffer = deque(maxlen=window_size * 2)  # 缓冲区大小为窗口的2倍
        
        logger.info(f"音频缓冲区初始化: 窗口={window_size}, 跳跃={self.hop_size}")
    
    def add_audio(self, audio_chunk: np.ndarray) -> Optional[np.ndarray]:
        """
        添加音频块并返回完整窗口（如果可用）
        
        Args:
            audio_chunk: 新的音频数据
            
        Returns:
            如果缓冲区有足够数据，返回窗口音频；否则返回 None
        """
        # 添加新音频到缓冲区
        self.buffer.extend(audio_chunk.flatten())
        
        # 检查是否有足够的数据形成完整窗口
        if len(self.buffer) >= self.window_size:
            # 提取最新的窗口数据
            window_data = np.array(list(self.buffer)[-self.window_size:])
            return window_data
        
        return None
    
    def get_current_window(self) -> Optional[np.ndarray]:
        """获取当前窗口数据"""
        if len(self.buffer) >= self.window_size:
            return np.array(list(self.buffer)[-self.window_size:])
        return None
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()


class WakeWordDetector:
    """
    Hey, 艾比 唤醒词检测器
    
    集成 MFCC 特征提取和 ONNX 模型推理，支持实时检测
    """
    
    def __init__(
        self,
        model_path: str,
        threshold: float = 0.85,
        sample_rate: int = 16000,
        window_size: int = 16000,
        overlap_ratio: float = 0.5
    ):
        """
        初始化唤醒词检测器
        
        Args:
            model_path: ONNX 模型文件路径
            threshold: 检测阈值
            sample_rate: 音频采样率
            window_size: 检测窗口大小（样本数）
            overlap_ratio: 窗口重叠比例
        """
        self.model_path = model_path
        self.threshold = threshold
        self.sample_rate = sample_rate
        
        # 初始化 ONNX 会话
        self.session = ort.InferenceSession(model_path)
        self.input_name = self.session.get_inputs()[0].name
        self.output_name = self.session.get_outputs()[0].name
        
        # 初始化 MFCC 提取器
        self.mfcc_extractor = MFCCExtractor(sample_rate=sample_rate)
        
        # 初始化音频缓冲区
        self.audio_buffer = AudioBuffer(window_size, overlap_ratio)
        
        # 统计信息
        self.detection_count = 0
        self.total_inferences = 0
        self.last_detection_time = 0
        
        logger.info(f"唤醒词检测器初始化完成:")
        logger.info(f"  模型: {model_path}")
        logger.info(f"  阈值: {threshold}")
        logger.info(f"  采样率: {sample_rate} Hz")
        logger.info(f"  窗口大小: {window_size} 样本 ({window_size/sample_rate:.1f}s)")
    
    def _run_inference(self, mfcc_features: np.ndarray) -> Tuple[bool, float]:
        """
        运行 ONNX 模型推理

        Args:
            mfcc_features: MFCC 特征，形状为 [1, 1, 100, 40]

        Returns:
            (is_detected, confidence): 检测结果和置信度
        """
        try:
            # 运行推理
            outputs = self.session.run(
                [self.output_name],
                {self.input_name: mfcc_features}
            )

            # 获取输出概率（模型已经输出 0-1 的概率值）
            confidence = float(outputs[0][0][0])

            # 确保概率值在合理范围内
            confidence = np.clip(confidence, 0.0, 1.0)

            # 判断是否检测到唤醒词
            is_detected = confidence >= self.threshold

            self.total_inferences += 1

            logger.debug(f"推理结果: confidence={confidence:.4f}, detected={is_detected}")

            return is_detected, confidence

        except Exception as e:
            logger.error(f"ONNX 推理失败: {e}")
            return False, 0.0
    
    def process_audio_chunk(self, audio_chunk: np.ndarray) -> Tuple[bool, float]:
        """
        处理音频块并检测唤醒词
        
        Args:
            audio_chunk: 音频数据块
            
        Returns:
            (is_detected, confidence): 检测结果和置信度
        """
        # 添加音频到缓冲区
        window_audio = self.audio_buffer.add_audio(audio_chunk)
        
        if window_audio is None:
            # 缓冲区数据不足，返回未检测
            return False, 0.0
        
        # 提取 MFCC 特征
        try:
            mfcc_features = self.mfcc_extractor.prepare_model_input(window_audio)
        except Exception as e:
            logger.error(f"MFCC 特征提取失败: {e}")
            return False, 0.0
        
        # 运行推理
        is_detected, confidence = self._run_inference(mfcc_features)
        
        # 更新统计信息
        if is_detected:
            self.detection_count += 1
            self.last_detection_time = time.time()
            logger.info(f"🎯 检测到唤醒词! 置信度: {confidence:.4f}")
        
        return is_detected, confidence
    
    def reset(self):
        """重置检测器状态"""
        self.audio_buffer.clear()
        logger.info("检测器状态已重置")
    
    def get_stats(self) -> dict:
        """获取检测器统计信息"""
        return {
            "detection_count": self.detection_count,
            "total_inferences": self.total_inferences,
            "detection_rate": self.detection_count / max(1, self.total_inferences),
            "last_detection_time": self.last_detection_time
        }


def test_wake_word_detector():
    """测试唤醒词检测器"""
    print("=== 测试唤醒词检测器 ===")
    
    # 模型路径（相对于项目根目录）
    model_path = "../../../../models/kws/hey_aibi.onnx"
    
    try:
        # 创建检测器
        detector = WakeWordDetector(
            model_path=model_path,
            threshold=0.85,
            sample_rate=16000
        )
        
        # 生成测试音频块
        chunk_size = 1024  # 每次处理 1024 样本
        sample_rate = 16000
        
        print(f"开始测试，每次处理 {chunk_size} 样本...")
        
        # 模拟多个音频块
        for i in range(20):  # 处理 20 个块，总共约 1.3 秒
            # 生成随机音频块
            audio_chunk = np.random.randn(chunk_size).astype(np.float32) * 0.1
            
            # 处理音频块
            is_detected, confidence = detector.process_audio_chunk(audio_chunk)
            
            if is_detected:
                print(f"块 {i+1}: 检测到唤醒词! 置信度: {confidence:.4f}")
            else:
                print(f"块 {i+1}: 未检测到 (置信度: {confidence:.4f})")
        
        # 显示统计信息
        stats = detector.get_stats()
        print(f"\n=== 检测统计 ===")
        print(f"总推理次数: {stats['total_inferences']}")
        print(f"检测次数: {stats['detection_count']}")
        print(f"检测率: {stats['detection_rate']:.2%}")
        
        print("✅ 唤醒词检测器测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_wake_word_detector()
