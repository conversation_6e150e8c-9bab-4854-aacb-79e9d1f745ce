#!/usr/bin/env python3
"""
MFCC 特征提取器
专为 Hey, 艾比 唤醒词模型设计，确保特征提取参数与训练时完全一致
"""

import numpy as np
import librosa
import logging
from typing import Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MFCCExtractor:
    """
    MFCC 特征提取器
    
    根据模型训练时的参数配置：
    - 采样率: 16kHz
    - MFCC 维度: 40
    - 窗长: 25ms (400 样本)
    - 帧移: 10ms (160 样本)
    - 时间帧数: 100 (对应 1 秒音频)
    """
    
    def __init__(
        self,
        sample_rate: int = 16000,
        n_mfcc: int = 40,
        win_length_ms: float = 25.0,
        hop_length_ms: float = 10.0,
        n_fft: Optional[int] = None,
        n_mels: int = 40,
        fmin: float = 0.0,
        fmax: Optional[float] = None
    ):
        """
        初始化 MFCC 提取器
        
        Args:
            sample_rate: 音频采样率 (Hz)
            n_mfcc: MFCC 系数数量
            win_length_ms: 窗长 (毫秒)
            hop_length_ms: 帧移 (毫秒)
            n_fft: FFT 点数，如果为 None 则自动计算
            n_mels: Mel 滤波器组数量
            fmin: 最低频率 (Hz)
            fmax: 最高频率 (Hz)，如果为 None 则为 sample_rate/2
        """
        self.sample_rate = sample_rate
        self.n_mfcc = n_mfcc
        self.n_mels = n_mels
        self.fmin = fmin
        self.fmax = fmax or sample_rate // 2
        
        # 计算窗长和帧移的样本数
        self.win_length = int(win_length_ms * sample_rate / 1000)
        self.hop_length = int(hop_length_ms * sample_rate / 1000)
        
        # 计算 FFT 点数（通常为窗长的最近 2 的幂次）
        if n_fft is None:
            self.n_fft = 2 ** int(np.ceil(np.log2(self.win_length)))
        else:
            self.n_fft = n_fft
        
        logger.info(f"MFCC 提取器初始化完成:")
        logger.info(f"  采样率: {self.sample_rate} Hz")
        logger.info(f"  MFCC 维度: {self.n_mfcc}")
        logger.info(f"  窗长: {win_length_ms}ms ({self.win_length} 样本)")
        logger.info(f"  帧移: {hop_length_ms}ms ({self.hop_length} 样本)")
        logger.info(f"  FFT 点数: {self.n_fft}")
        logger.info(f"  频率范围: {self.fmin}-{self.fmax} Hz")
    
    def extract(self, audio: np.ndarray) -> np.ndarray:
        """
        从音频信号中提取 MFCC 特征
        
        Args:
            audio: 音频信号，形状为 [samples]
            
        Returns:
            MFCC 特征，形状为 [time_frames, n_mfcc]
        """
        try:
            # 确保音频是浮点数格式
            if audio.dtype != np.float32:
                audio = audio.astype(np.float32)
            
            # 归一化音频信号
            if np.max(np.abs(audio)) > 0:
                audio = audio / np.max(np.abs(audio))
            
            # 提取 MFCC 特征
            mfcc = librosa.feature.mfcc(
                y=audio,
                sr=self.sample_rate,
                n_mfcc=self.n_mfcc,
                n_fft=self.n_fft,
                hop_length=self.hop_length,
                win_length=self.win_length,
                n_mels=self.n_mels,
                fmin=self.fmin,
                fmax=self.fmax,
                center=True,  # 在信号两端填充零
                pad_mode='constant'
            )
            
            # 转置为 [time_frames, n_mfcc] 格式
            mfcc = mfcc.T
            
            logger.debug(f"提取 MFCC 特征: {mfcc.shape}")
            
            return mfcc
            
        except Exception as e:
            logger.error(f"MFCC 特征提取失败: {e}")
            raise
    
    def extract_fixed_length(
        self, 
        audio: np.ndarray, 
        target_frames: int = 100
    ) -> np.ndarray:
        """
        提取固定长度的 MFCC 特征
        
        Args:
            audio: 音频信号
            target_frames: 目标时间帧数
            
        Returns:
            固定长度的 MFCC 特征，形状为 [target_frames, n_mfcc]
        """
        mfcc = self.extract(audio)
        
        # 调整到目标长度
        if mfcc.shape[0] < target_frames:
            # 如果特征帧数不足，进行零填充
            padding = target_frames - mfcc.shape[0]
            mfcc = np.pad(mfcc, ((0, padding), (0, 0)), mode='constant')
        elif mfcc.shape[0] > target_frames:
            # 如果特征帧数过多，截取中间部分
            start = (mfcc.shape[0] - target_frames) // 2
            mfcc = mfcc[start:start + target_frames]
        
        return mfcc
    
    def prepare_model_input(self, audio: np.ndarray) -> np.ndarray:
        """
        准备模型输入格式的特征
        
        Args:
            audio: 音频信号，应为 1 秒长度 (16000 样本)
            
        Returns:
            模型输入格式的特征，形状为 [1, 1, 100, 40]
        """
        # 提取固定长度的 MFCC 特征
        mfcc = self.extract_fixed_length(audio, target_frames=100)
        
        # 调整形状为模型输入格式 [batch, channel, time, features]
        model_input = mfcc.reshape(1, 1, 100, self.n_mfcc)
        
        return model_input.astype(np.float32)
    
    def get_expected_audio_length(self, target_frames: int = 100) -> int:
        """
        计算产生指定帧数所需的音频长度
        
        Args:
            target_frames: 目标时间帧数
            
        Returns:
            所需的音频样本数
        """
        return (target_frames - 1) * self.hop_length + self.win_length


def test_mfcc_extractor():
    """测试 MFCC 特征提取器"""
    print("=== 测试 MFCC 特征提取器 ===")
    
    # 创建提取器
    extractor = MFCCExtractor()
    
    # 生成测试音频 (1 秒，16kHz)
    duration = 1.0
    sample_rate = 16000
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 生成包含多个频率成分的测试信号
    audio = (
        0.3 * np.sin(2 * np.pi * 440 * t) +  # A4 音符
        0.2 * np.sin(2 * np.pi * 880 * t) +  # A5 音符
        0.1 * np.random.randn(len(t))        # 噪声
    )
    
    print(f"测试音频: {audio.shape}, 长度: {len(audio)/sample_rate:.2f}s")
    
    # 提取 MFCC 特征
    mfcc = extractor.extract(audio)
    print(f"MFCC 特征: {mfcc.shape}")
    
    # 提取固定长度特征
    mfcc_fixed = extractor.extract_fixed_length(audio, target_frames=100)
    print(f"固定长度 MFCC: {mfcc_fixed.shape}")
    
    # 准备模型输入
    model_input = extractor.prepare_model_input(audio)
    print(f"模型输入: {model_input.shape}")
    
    # 验证特征统计信息
    print(f"特征范围: [{mfcc_fixed.min():.3f}, {mfcc_fixed.max():.3f}]")
    print(f"特征均值: {mfcc_fixed.mean():.3f}")
    print(f"特征标准差: {mfcc_fixed.std():.3f}")
    
    print("✅ MFCC 特征提取器测试通过!")


if __name__ == "__main__":
    test_mfcc_extractor()
