# Rhasspy 3.0 配置文件
# Hey, 艾比 智能语音助手配置

# 覆盖默认配置，添加自定义程序
programs:

  # -----------
  # 音频输入
  # -----------
  mic:
    # ALSA 录音（推荐用于生产环境）
    arecord:
      command: |
        arecord -q -D "${device}" -r 16000 -c 1 -f S16_LE -t raw -
      adapter: |
        mic_adapter_raw.py --samples-per-chunk 1024 --rate 16000 --width 2 --channels 1
      template_args:
        device: "default"

    # SoundDevice 录音（推荐用于开发测试）
    sounddevice:
      command: |
        script/events
      adapter: |
        mic_adapter_raw.py --samples-per-chunk 1024 --rate 16000 --width 2 --channels 1

  # -------------------
  # 唤醒词检测
  # -------------------
  wake:
    # Hey, 艾比 自定义唤醒词（通过符号链接）
    hey-aibi:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/wake/hey-aibi/bin/hey_aibi_raw_text.py --model "${model}" --threshold ${threshold} --wake-word "${wake_word}" --samples-per-chunk ${samples_per_chunk}
      adapter: |
        wake_adapter_raw.py
      template_args:
        model: "/home/<USER>/project/Aibi-Rhasspy/config/programs/wake/hey-aibi/models/kws/hey_aibi.onnx"
        threshold: 0.5
        wake_word: "hey_aibi"
        samples_per_chunk: 1024

  # ------------------------
  # 语音活动检测 (VAD)
  # ------------------------
  vad:
    # Silero VAD
    silero:
      command: |
        script/speech_prob "${model}"
      adapter: |
        bin/vad_adapter_raw.py --rate 16000 --width 2 --channels 1 --samples-per-chunk 512
      template_args:
        model: "share/silero_vad.onnx"

  # ------------------------
  # 语音识别 (ASR)
  # ------------------------
  asr:
    # SenseVoiceSmall 语音识别（通过符号链接）
    sensevoice:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/asr/sensevoice/bin/sensevoice_wav2text.py "${wav_file}" --model "${model}" --language "${language}" --device "${device}" --use-vad "${use_vad}" --use-punc "${use_punc}"
      adapter: |
        asr_adapter_wav2text.py
      template_args:
        model: "/home/<USER>/project/Aibi-Rhasspy/config/programs/asr/sensevoice/models/asr/SenseVoiceSmall"
        language: "auto"
        device: "cpu"
        use_vad: true
        use_punc: true

    # 占位符 - 备用
    placeholder:
      command: |
        echo '{"text": "placeholder asr result"}'
      shell: true

  # ------------------------
  # 意图理解 (NLU)
  # ------------------------
  intent:
    # Dify 客户端（需要 API 密钥）
    dify:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/intent/dify/bin/dify_intent.py "${text}" --base-url "${base_url}" --api-key "${api_key}" --user "${user}" --timeout "${timeout}" --format json
      template_args:
        base_url: "http://11.20.60.13"
        api_key: "app-B4iImX8Ot46wmAHIhRUtJWzw"  # 请修改为您的 Dify API 密钥
        user: "rhasspy-user"
        timeout: "30"

    # 占位符 - 备用
    placeholder:
      command: |
        echo '{"success": false, "error": "请配置 Dify API"}'
      shell: true

  # ------------------------
  # 意图处理 (Handle)
  # ------------------------
  handle:
    # Dify 响应提取（从 intent 结果中提取响应）
    dify-extract:
      command: |
        conda run -n aibi python -c "
        import json, sys
        try:
            with open('${intent_file}', 'r') as f:
                data = json.load(f)
            if data.get('success'):
                print(data.get('response', '处理完成'))
            else:
                print(f\"错误: {data.get('error', '未知错误')}\")
        except Exception as e:
            print(f'处理失败: {e}')
        "

    # 简单回显处理
    echo:
      command: |
        echo "收到指令: $1"
      shell: true

  # ------------------------
  # 语音合成 (TTS)
  # ------------------------
  tts:
    # CosyVoice-300M SFT 模式（推荐）
    cosyvoice-sft:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --speaker "${speaker}" --model-type sft --model "/home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/models/CosyVoice-300M"
      adapter: |
        tts_adapter_text2wav.py
      template_args:
        speaker: "中文女"

    # CosyVoice-300M 指令模式（情感控制）
    cosyvoice-instruct:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --speaker "${speaker}" --model-type instruct --instruct "${instruct}"
      adapter: |
        tts_adapter_text2wav.py
      template_args:
        speaker: "中文女"
        instruct: "用自然的语调说话"

    # CosyVoice-300M 零样本模式（音色克隆）
    cosyvoice-clone:
      command: |
        conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/bin/cosyvoice_text2wav.py "${text}" --output "${output_file}" --prompt-audio "${prompt_audio}" --prompt-text "${prompt_text}" --model-type base --model "/home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/models/CosyVoice-300M"
      adapter: |
        tts_adapter_text2wav.py
      template_args:
        prompt_audio: "/home/<USER>/project/Aibi-Rhasspy/config/programs/tts/cosyvoice/models/CosyVoice-300M/asset/zero_shot_prompt.wav"
        prompt_text: "希望你以后能够做的比我还好呦。"

    # 占位符 - 备用
    placeholder:
      command: |
        conda run -n aibi bash -c 'echo "TTS placeholder" | text2wave -o /dev/stdout'
      shell: true

  # -----------
  # 音频输出
  # -----------
  snd:
    # ALSA 播放
    aplay:
      command: |
        conda run -n aibi aplay -q -D "${device}" -r 22050 -c 1 -f S16_LE -t raw
      adapter: |
        conda run -n aibi python snd_adapter_raw.py --rate 22050 --width 2 --channels 1
      template_args:
        device: "default"

# 管道配置
# 必需的空字段（继承默认配置）
satellites:

servers:

pipelines:
  # 主管道 - Hey, 艾比 完整语音助手
  default:
    mic:
      name: arecord
    wake:
      name: hey-aibi
    asr:
      name: sensevoice
    intent:
      name: dify
    handle:
      name: dify-extract
    tts:
      name: cosyvoice-clone
    snd:
      name: aplay

  # 测试管道 - 仅唤醒词检测
  wake_only:
    mic:
      name: arecord
    wake:
      name: hey-aibi

# 注意：Rhasspy 3.0 不支持 settings 字段
# 所有配置都通过 programs 的 template_args 传递
