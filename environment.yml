name: aibi
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  # Python 基础
  - python=3.10
  
  # 数值计算
  - numpy>=1.21.0
  - scipy>=1.7.0
  
  # 音频处理
  - librosa>=0.10.0
  - soundfile>=0.12.0
  - ffmpeg
  
  # 深度学习框架
  - pytorch>=1.13.0
  - torchaudio>=0.13.0
  
  # 系统工具
  - pip
  - git
  
  # 通过 pip 安装的包
  - pip:
    # FunASR 和 ModelScope
    - funasr>=1.0.0
    - modelscope>=1.9.0

    # ONNX 运行时
    - onnxruntime>=1.15.0

    # 中文处理
    - jieba>=0.42.0

    # 音频工具
    - pydub>=0.25.0

    # Web 服务器（rhasspy3 需要）
    - fastapi>=0.68.0
    - uvicorn>=0.15.0
    - websockets>=10.0

    # 其他工具
    - requests>=2.28.0
    - tqdm>=4.64.0
    - pyyaml>=6.0
