# 🎯 Rhasspy 3.0 智能语音助手

基于 Rhasspy 3.0 的智能语音助手项目，集成自定义 ONNX 唤醒词模型、SenseVoiceSmall ASR 模型和 CosyVoice-300M TTS 模型，实现完全本地化的语音交互体验。

## ✨ **核心特性**

- 🔊 **自定义唤醒词**：支持 ONNX 格式的自训练唤醒词模型
- 🎤 **高精度 ASR**：集成阿里达摩院 SenseVoiceSmall 多语言语音识别
- 🗣️ **自然 TTS**：使用 CosyVoice-300M 实现高质量语音合成
- 🏠 **完全本地化**：所有处理均在本地完成，保护隐私安全
- 🔧 **模块化架构**：基于 Wyoming 协议的可扩展设计
- 🚀 **高性能**：优化的流式处理和并发机制

## 🏗️ **系统架构**

```
音频输入 → VAD检测 → 唤醒词检测 → ASR识别 → 意图理解 → TTS合成 → 音频输出
    ↓         ↓         ↓         ↓        ↓        ↓        ↓
  麦克风   Silero VAD  ONNX模型  SenseVoice  NLU   CosyVoice  扬声器
```

## 🚀 **快速开始**

### **一键安装**
```bash
# 克隆项目
git clone <your-repo-url> Aibi-Rhasspy
cd Aibi-Rhasspy

# 一键安装和配置
chmod +x quick_start.sh
./quick_start.sh
```

### **手动安装**
```bash
# 1. 配置环境
chmod +x setup_environment.sh
./setup_environment.sh

# 2. 获取源码
chmod +x clone_rhasspy.sh
./clone_rhasspy.sh

# 3. 激活环境
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt
```

## 📁 **项目结构**

```
Aibi-Rhasspy/
├── 📂 rhasspy3/              # Rhasspy 3.0 核心
├── 📂 services/              # 自定义服务
│   ├── 🔊 wake_word/        # 唤醒词服务
│   ├── 🎤 asr/              # ASR 服务
│   └── 🗣️ tts/              # TTS 服务
├── 📂 models/               # 模型文件
│   ├── wake_word/           # 唤醒词模型
│   ├── asr/                 # ASR 模型
│   └── tts/                 # TTS 模型
├── 📂 configs/              # 配置文件
├── 📂 scripts/              # 工具脚本
├── 📂 tests/                # 测试文件
└── 📂 docs/                 # 项目文档
```

## ⚙️ **配置说明**

### **主配置文件** (`configs/pipeline.yaml`)
```yaml
pipelines:
  main:
    wake:
      model_path: models/wake_word/custom_wake.onnx
      threshold: 0.8
    asr:
      model_path: models/asr/SenseVoiceSmall
      language: zh-cn
    tts:
      model_path: models/tts/CosyVoice-300M
      voice_id: default
```

### **模型下载**
```bash
# 下载 SenseVoiceSmall 模型
python3 scripts/download_sensevoice.py

# 下载 CosyVoice-300M 模型
python3 scripts/download_cosyvoice.py

# 放置自定义唤醒词模型
cp your_wake_model.onnx models/wake_word/custom_wake.onnx
```

## 🔧 **使用方法**

### **启动服务**
```bash
# 激活环境
source venv/bin/activate

# 启动主服务
python3 -m rhasspy3 --config configs/pipeline.yaml

# 或使用脚本启动
python3 scripts/start_services.py
```

### **测试功能**
```bash
# 测试唤醒词
python3 tests/test_wake_word.py

# 测试 ASR
python3 tests/test_asr.py

# 测试 TTS
python3 tests/test_tts.py

# 端到端测试
python3 scripts/test_pipeline.py
```

### **Web 界面**
访问 `http://localhost:13331` 使用 Web 界面进行配置和测试。

## 📊 **性能指标**

| 组件 | 延迟 | 准确率 | 内存占用 |
|------|------|--------|----------|
| 唤醒词检测 | <100ms | >95% | <50MB |
| ASR 识别 | <500ms | >90% | <500MB |
| TTS 合成 | <300ms | 自然度高 | <800MB |

## 🔧 **开发指南**

### **添加自定义服务**
```python
# services/custom/my_service.py
from rhasspy3.core import Service

class MyCustomService(Service):
    def process(self, data):
        # 自定义处理逻辑
        return processed_data
```

### **扩展意图处理**
```yaml
# configs/intents.yaml
intents:
  custom_intent:
    patterns:
      - "自定义指令"
    responses:
      - "执行自定义操作"
```

## 🐛 **故障排除**

### **常见问题**
1. **音频设备问题**
   ```bash
   # 检查音频设备
   arecord -l
   aplay -l
   ```

2. **模型加载失败**
   ```bash
   # 检查模型路径和权限
   ls -la models/
   ```

3. **依赖安装问题**
   ```bash
   # 重新安装依赖
   pip install --force-reinstall -r requirements.txt
   ```

### **日志查看**
```bash
# 查看服务日志
tail -f logs/rhasspy.log

# 查看错误日志
grep ERROR logs/rhasspy.log
```

## 📚 **文档链接**

- [架构设计](project_architecture.md)
- [API 文档](docs/API.md)
- [部署指南](docs/deployment.md)
- [故障排除](docs/troubleshooting.md)

## 🤝 **贡献指南**

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 **致谢**

- [Rhasspy](https://github.com/rhasspy/rhasspy3) - 开源语音助手框架
- [SenseVoice](https://github.com/FunAudioLLM/SenseVoice) - 多语言语音识别模型
- [CosyVoice](https://github.com/FunAudioLLM/CosyVoice) - 多语言语音合成模型

---

**🚀 开始您的智能语音助手之旅吧！**
