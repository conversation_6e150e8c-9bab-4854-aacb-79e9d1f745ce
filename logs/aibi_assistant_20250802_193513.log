INFO:mfcc_extractor:MFCC 提取器初始化完成:
INFO:mfcc_extractor:  采样率: 16000 Hz
INFO:mfcc_extractor:  MFCC 维度: 40
INFO:mfcc_extractor:  窗长: 25.0ms (400 样本)
INFO:mfcc_extractor:  帧移: 10.0ms (160 样本)
INFO:mfcc_extractor:  FFT 点数: 512
INFO:mfcc_extractor:  频率范围: 0.0-8000 Hz
INFO:wake_word_detector:音频缓冲区初始化: 窗口=16000, 跳跃=8000
INFO:wake_word_detector:唤醒词检测器初始化完成:
INFO:wake_word_detector:  模型: /home/<USER>/project/Aibi-Rhasspy/config/programs/wake/hey-aibi/models/kws/hey_aibi.onnx
INFO:wake_word_detector:  阈值: 0.5
INFO:wake_word_detector:  采样率: 16000 Hz
INFO:wake_word_detector:  窗口大小: 16000 样本 (1.0s)
INFO:hey_aibi_raw_text:开始监听唤醒词: hey_aibi
INFO:hey_aibi_raw_text:每次处理 1024 样本
INFO:hey_aibi_raw_text:音频流结束

Traceback (most recent call last):
  File "/home/<USER>/project/Aibi-Rhasspy/rhasspy3/bin/wake_adapter_raw.py", line 63, in main
    proc.stdin.flush()
BrokenPipeError: [Errno 32] Broken pipe

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/project/Aibi-Rhasspy/rhasspy3/bin/wake_adapter_raw.py", line 87, in <module>
    main()
  File "/home/<USER>/project/Aibi-Rhasspy/rhasspy3/bin/wake_adapter_raw.py", line 41, in main
    with subprocess.Popen(
  File "/usr/lib/python3.10/subprocess.py", line 1044, in __exit__
    self.stdin.close()
BrokenPipeError: [Errno 32] Broken pipe

{"wake_detection": null, "asr_transcript": null, "intent_result": null, "handle_result": null}

