2025-08-02 20:53:02,034 - INFO - 🚀 开始 Rhasspy3 系统性测试
2025-08-02 20:53:02,035 - INFO - 测试项目: 4 个阶段
2025-08-02 20:53:02,035 - INFO - 🔍 检查测试环境...
2025-08-02 20:53:02,475 - INFO - ✅ conda aibi 环境存在
2025-08-02 20:53:02,475 - INFO - ✅ 测试环境检查通过
2025-08-02 20:53:02,475 - INFO - 
📍 当前进度: 1/4
2025-08-02 20:53:02,475 - INFO - ================================================================================
2025-08-02 20:53:02,475 - INFO - 🧪 开始测试阶段: mic - 麦克风输入测试
2025-08-02 20:53:02,475 - INFO - ================================================================================
2025-08-02 20:53:02,475 - INFO - 执行命令: conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/test/mic/test_mic.py
2025-08-02 20:53:11,541 - INFO - ✅ mic 测试通过
2025-08-02 20:53:21,148 - INFO - 
📍 当前进度: 2/4
2025-08-02 20:53:21,148 - INFO - ================================================================================
2025-08-02 20:53:21,148 - INFO - 🧪 开始测试阶段: wake - 唤醒词检测测试
2025-08-02 20:53:21,148 - INFO - ================================================================================
2025-08-02 20:53:21,148 - INFO - 执行命令: conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/test/wake/test_wake.py
2025-08-02 20:53:26,027 - INFO - ✅ wake 测试通过
2025-08-02 20:58:52,690 - INFO - 
用户中断测试
2025-08-02 20:58:52,690 - INFO - ================================================================================
2025-08-02 20:58:52,690 - INFO - 📊 Rhasspy3 系统性测试结果汇总
2025-08-02 20:58:52,690 - INFO - ================================================================================
2025-08-02 20:58:52,690 - INFO -    mic          (必需): ✅ 通过 - 麦克风输入测试
2025-08-02 20:58:52,690 - INFO -    wake         (必需): ✅ 通过 - 唤醒词检测测试
2025-08-02 20:58:52,690 - INFO -    asr          (跳过): ⏭️  未执行 - 语音识别测试
2025-08-02 20:58:52,690 - INFO -    integration  (跳过): ⏭️  未执行 - 集成测试
2025-08-02 20:58:52,690 - INFO - 
总计: 2/2 项测试通过 (通过率: 100.0%)
2025-08-02 20:58:52,690 - INFO - 🌟 优秀！系统运行状态良好
2025-08-02 20:58:52,690 - INFO - 🎉 系统性测试基本通过！
2025-08-02 21:00:03,109 - INFO - 🚀 开始 Rhasspy3 系统性测试
2025-08-02 21:00:03,109 - INFO - 测试项目: 4 个阶段
2025-08-02 21:00:03,109 - INFO - 🔍 检查测试环境...
2025-08-02 21:00:03,531 - INFO - ✅ conda aibi 环境存在
2025-08-02 21:00:03,531 - INFO - ✅ 测试环境检查通过
2025-08-02 21:00:03,531 - INFO - 
📍 当前进度: 1/4
2025-08-02 21:00:03,531 - INFO - ================================================================================
2025-08-02 21:00:03,531 - INFO - 🧪 开始测试阶段: mic - 麦克风输入测试
2025-08-02 21:00:03,531 - INFO - ================================================================================
2025-08-02 21:00:03,531 - INFO - 执行命令: conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/test/mic/test_mic.py
2025-08-02 21:00:12,619 - INFO - ✅ mic 测试通过
2025-08-02 21:00:15,043 - INFO - 
📍 当前进度: 2/4
2025-08-02 21:00:15,043 - INFO - ================================================================================
2025-08-02 21:00:15,043 - INFO - 🧪 开始测试阶段: wake - 唤醒词检测测试
2025-08-02 21:00:15,043 - INFO - ================================================================================
2025-08-02 21:00:15,043 - INFO - 执行命令: conda run -n aibi python /home/<USER>/project/Aibi-Rhasspy/test/wake/test_wake.py
2025-08-02 21:00:28,584 - INFO - ✅ wake 测试通过
2025-08-02 21:02:30,166 - INFO - 
用户中断测试
2025-08-02 21:02:30,166 - INFO - ================================================================================
2025-08-02 21:02:30,166 - INFO - 📊 Rhasspy3 系统性测试结果汇总
2025-08-02 21:02:30,166 - INFO - ================================================================================
2025-08-02 21:02:30,166 - INFO -    mic          (必需): ✅ 通过 - 麦克风输入测试
2025-08-02 21:02:30,166 - INFO -    wake         (必需): ✅ 通过 - 唤醒词检测测试
2025-08-02 21:02:30,166 - INFO -    asr          (跳过): ⏭️  未执行 - 语音识别测试
2025-08-02 21:02:30,166 - INFO -    integration  (跳过): ⏭️  未执行 - 集成测试
2025-08-02 21:02:30,166 - INFO - 
总计: 2/2 项测试通过 (通过率: 100.0%)
2025-08-02 21:02:30,166 - INFO - 🌟 优秀！系统运行状态良好
2025-08-02 21:02:30,166 - INFO - 🎉 系统性测试基本通过！
