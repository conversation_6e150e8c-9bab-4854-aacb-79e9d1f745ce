#!/usr/bin/env python3
"""
Rhasspy3 语音识别测试脚本
测试 SenseVoiceSmall ASR 功能

功能：
1. 测试 SenseVoiceSmall 模型加载
2. 测试中文语音识别
3. 测试英文语音识别
4. 测试混合语言识别
5. 验证识别准确率
"""

import os
import sys
import time
import wave
import subprocess
import tempfile
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test/asr/asr_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ASRTester:
    def __init__(self):
        self.project_root = Path("/home/<USER>/project/Aibi-Rhasspy")
        self.asr_program_path = self.project_root / "config/programs/asr/sensevoice"
        self.model_path = self.asr_program_path / "models/asr/SenseVoiceSmall"
        self.script_path = self.asr_program_path / "bin/sensevoice_wav2text.py"
        
    def check_model_exists(self):
        """检查 SenseVoiceSmall 模型是否存在"""
        logger.info("🔍 检查 SenseVoiceSmall 模型...")
        
        if self.model_path.exists():
            # 检查模型文件
            model_files = list(self.model_path.glob("*"))
            logger.info(f"✅ 模型目录存在: {self.model_path}")
            logger.info(f"   包含文件数: {len(model_files)}")
            
            # 检查关键文件
            key_files = ['config.json', 'pytorch_model.bin', 'tokenizer.json']
            for key_file in key_files:
                file_path = self.model_path / key_file
                if file_path.exists():
                    logger.info(f"   ✅ {key_file}")
                else:
                    logger.warning(f"   ⚠️  {key_file} 缺失")
            
            return True
        else:
            logger.error(f"❌ 模型目录不存在: {self.model_path}")
            return False
    
    def check_script_exists(self):
        """检查 ASR 脚本是否存在"""
        logger.info("📜 检查 ASR 脚本...")
        
        if self.script_path.exists():
            logger.info(f"✅ ASR 脚本存在: {self.script_path}")
            return True
        else:
            logger.error(f"❌ ASR 脚本不存在: {self.script_path}")
            return False
    
    def check_dependencies(self):
        """检查依赖库"""
        logger.info("📦 检查依赖库...")
        
        dependencies = [
            'torch',
            'transformers', 
            'librosa',
            'numpy',
            'soundfile'
        ]
        
        missing_deps = []
        
        for dep in dependencies:
            try:
                __import__(dep)
                logger.info(f"✅ {dep}")
            except ImportError:
                logger.error(f"❌ {dep} 未安装")
                missing_deps.append(dep)
        
        if missing_deps:
            logger.error(f"缺少依赖: {', '.join(missing_deps)}")
            return False
        
        return True
    
    def test_chinese_recognition(self):
        """测试中文语音识别"""
        logger.info("🇨🇳 测试中文语音识别...")
        
        return self._test_recognition(
            language="zh",
            prompt="请说一句中文，例如：你好，我是艾比",
            expected_keywords=["你好", "艾比", "中文"]
        )
    
    def test_english_recognition(self):
        """测试英文语音识别"""
        logger.info("🇺🇸 测试英文语音识别...")
        
        return self._test_recognition(
            language="en", 
            prompt="Please say something in English, like: Hello, I am Aibi",
            expected_keywords=["hello", "aibi", "english"]
        )
    
    def test_auto_language_detection(self):
        """测试自动语言检测"""
        logger.info("🌐 测试自动语言检测...")
        
        return self._test_recognition(
            language="auto",
            prompt="请说中英文混合句子，例如：Hello 艾比，how are you？",
            expected_keywords=["hello", "艾比", "how"]
        )
    
    def _test_recognition(self, language, prompt, expected_keywords=None):
        """执行语音识别测试"""
        try:
            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                wav_file = tmp_file.name
            
            # 录制音频
            record_cmd = [
                'arecord',
                '-D', 'default',
                '-r', '16000',
                '-c', '1',
                '-f', 'S16_LE',
                '-d', '5',
                wav_file
            ]
            
            logger.info(f"🎤 {prompt} (5秒录音)...")
            record_result = subprocess.run(record_cmd, capture_output=True, text=True)
            
            if record_result.returncode != 0:
                logger.error("❌ 录音失败")
                return False
            
            # 执行语音识别
            asr_cmd = [
                'conda', 'run', '-n', 'aibi', 'python',
                str(self.script_path),
                wav_file,
                '--model', str(self.model_path),
                '--language', language,
                '--device', 'cpu',
                '--use-vad',
                '--use-punc'
            ]
            
            logger.info("🧠 执行语音识别...")
            start_time = time.time()
            asr_result = subprocess.run(asr_cmd, capture_output=True, text=True, timeout=60)
            end_time = time.time()
            
            if asr_result.returncode == 0:
                # 解析识别结果
                try:
                    result_data = json.loads(asr_result.stdout)
                    recognized_text = result_data.get('text', '').strip()
                    
                    logger.info(f"✅ 识别成功 (耗时: {end_time - start_time:.2f}秒)")
                    logger.info(f"   识别结果: {recognized_text}")
                    logger.info(f"   语言: {language}")
                    
                    # 检查关键词（如果提供）
                    if expected_keywords and recognized_text:
                        found_keywords = []
                        for keyword in expected_keywords:
                            if keyword.lower() in recognized_text.lower():
                                found_keywords.append(keyword)
                        
                        if found_keywords:
                            logger.info(f"   匹配关键词: {found_keywords}")
                        else:
                            logger.info("   未匹配到预期关键词（可能是口音或噪音影响）")
                    
                    return True
                    
                except json.JSONDecodeError:
                    # 如果不是 JSON 格式，直接输出文本
                    recognized_text = asr_result.stdout.strip()
                    logger.info(f"✅ 识别成功: {recognized_text}")
                    return True
                    
            else:
                error_msg = asr_result.stderr
                logger.error(f"❌ 语音识别失败: {error_msg}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 语音识别超时")
            return False
        except Exception as e:
            logger.error(f"❌ 语音识别测试异常: {e}")
            return False
        finally:
            if 'wav_file' in locals() and os.path.exists(wav_file):
                os.unlink(wav_file)
    
    def test_batch_recognition(self):
        """测试批量识别性能"""
        logger.info("⚡ 测试批量识别性能...")
        
        try:
            # 创建多个测试音频文件
            test_files = []
            for i in range(3):
                with tempfile.NamedTemporaryFile(suffix=f'_test_{i}.wav', delete=False) as tmp_file:
                    wav_file = tmp_file.name
                    test_files.append(wav_file)
                
                # 录制短音频
                record_cmd = [
                    'arecord',
                    '-D', 'default',
                    '-r', '16000',
                    '-c', '1',
                    '-f', 'S16_LE',
                    '-d', '2',
                    wav_file
                ]
                
                logger.info(f"录制测试音频 {i+1}/3 (2秒)...")
                subprocess.run(record_cmd, capture_output=True)
            
            # 批量识别
            total_time = 0
            successful_recognitions = 0
            
            for i, wav_file in enumerate(test_files):
                asr_cmd = [
                    'conda', 'run', '-n', 'aibi', 'python',
                    str(self.script_path),
                    wav_file,
                    '--model', str(self.model_path),
                    '--language', 'auto',
                    '--device', 'cpu',
                    '--use-vad',
                    '--use-punc'
                ]
                
                start_time = time.time()
                result = subprocess.run(asr_cmd, capture_output=True, text=True, timeout=30)
                end_time = time.time()
                
                recognition_time = end_time - start_time
                total_time += recognition_time
                
                if result.returncode == 0:
                    successful_recognitions += 1
                    logger.info(f"   测试 {i+1}: 成功 (耗时: {recognition_time:.2f}秒)")
                else:
                    logger.info(f"   测试 {i+1}: 失败")
            
            avg_time = total_time / len(test_files)
            success_rate = successful_recognitions / len(test_files) * 100
            
            logger.info(f"✅ 批量测试完成")
            logger.info(f"   成功率: {success_rate:.1f}%")
            logger.info(f"   平均耗时: {avg_time:.2f}秒")
            
            return success_rate >= 66.7  # 至少2/3成功
            
        except Exception as e:
            logger.error(f"❌ 批量识别测试异常: {e}")
            return False
        finally:
            # 清理测试文件
            for wav_file in test_files:
                if os.path.exists(wav_file):
                    os.unlink(wav_file)
    
    def run_all_tests(self):
        """运行所有 ASR 测试"""
        logger.info("=" * 60)
        logger.info("🗣️  Rhasspy3 语音识别测试开始")
        logger.info("=" * 60)
        
        results = {}
        
        # 测试 1: 检查模型
        results['model_exists'] = self.check_model_exists()
        
        # 测试 2: 检查脚本
        results['script_exists'] = self.check_script_exists()
        
        # 测试 3: 检查依赖
        results['dependencies'] = self.check_dependencies()
        
        # 如果基础检查通过，进行功能测试
        if all([results['model_exists'], results['script_exists'], results['dependencies']]):
            # 测试 4: 中文识别
            results['chinese_recognition'] = self.test_chinese_recognition()
            
            # 测试 5: 英文识别
            results['english_recognition'] = self.test_english_recognition()
            
            # 测试 6: 自动语言检测
            results['auto_language'] = self.test_auto_language_detection()
            
            # 测试 7: 批量识别性能
            results['batch_recognition'] = self.test_batch_recognition()
        else:
            logger.warning("⚠️  跳过功能测试（基础检查未通过）")
            results.update({
                'chinese_recognition': False,
                'english_recognition': False,
                'auto_language': False,
                'batch_recognition': False
            })
        
        # 汇总结果
        logger.info("=" * 60)
        logger.info("📊 ASR 测试结果汇总:")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        logger.info(f"\n总计: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests >= total_tests - 2:  # 允许2个测试失败
            logger.info("🎉 ASR 测试基本通过！")
            return True
        else:
            logger.warning("⚠️  ASR 测试失败较多，请检查配置")
            return False

def main():
    """主函数"""
    tester = ASRTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ ASR 测试完成，可以进入下一阶段测试")
        print("请确认语音识别功能正常后，输入 'y' 继续下一阶段测试...")
    else:
        print("\n❌ ASR 测试失败，请检查模型和配置")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
