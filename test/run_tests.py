#!/usr/bin/env python3
"""
Rhasspy3 系统性测试主脚本
按照语音处理管道顺序执行所有测试

测试顺序：
1. mic (麦克风输入)
2. wake (唤醒词检测) 
3. asr (语音识别)
4. intent (意图理解)
5. handle (意图处理)
6. tts (语音合成)
7. snd (音频输出)
8. integration (集成测试)
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test/test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TestRunner:
    def __init__(self):
        self.project_root = Path("/home/<USER>/project/Aibi-Rhasspy")
        self.test_root = self.project_root / "test"
        
        # 测试顺序和配置
        self.test_stages = [
            {
                'name': 'mic',
                'description': '麦克风输入测试',
                'script': 'test/mic/test_mic.py',
                'required': True
            },
            {
                'name': 'wake', 
                'description': '唤醒词检测测试',
                'script': 'test/wake/test_wake.py',
                'required': True
            },
            {
                'name': 'asr',
                'description': '语音识别测试', 
                'script': 'test/asr/test_asr.py',
                'required': True
            },
            {
                'name': 'integration',
                'description': '集成测试',
                'script': 'test/integration/test_integration.py',
                'required': False
            }
        ]
        
        self.test_results = {}
    
    def check_environment(self):
        """检查测试环境"""
        logger.info("🔍 检查测试环境...")
        
        # 检查 conda 环境
        try:
            result = subprocess.run(
                ['conda', 'info', '--envs'],
                capture_output=True,
                text=True
            )
            if 'aibi' in result.stdout:
                logger.info("✅ conda aibi 环境存在")
            else:
                logger.error("❌ conda aibi 环境不存在")
                return False
        except FileNotFoundError:
            logger.error("❌ conda 未安装")
            return False
        
        # 检查项目目录
        if not self.project_root.exists():
            logger.error(f"❌ 项目目录不存在: {self.project_root}")
            return False
        
        # 检查配置文件
        config_file = self.project_root / "config/configuration.yaml"
        if not config_file.exists():
            logger.error(f"❌ 配置文件不存在: {config_file}")
            return False
        
        logger.info("✅ 测试环境检查通过")
        return True
    
    def run_test_stage(self, stage):
        """运行单个测试阶段"""
        logger.info("=" * 80)
        logger.info(f"🧪 开始测试阶段: {stage['name']} - {stage['description']}")
        logger.info("=" * 80)
        
        script_path = self.project_root / stage['script']
        
        if not script_path.exists():
            logger.error(f"❌ 测试脚本不存在: {script_path}")
            return False
        
        try:
            # 执行测试脚本
            cmd = [
                'conda', 'run', '-n', 'aibi', 'python', str(script_path)
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=False,  # 让输出直接显示
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {stage['name']} 测试通过")
                return True
            else:
                logger.error(f"❌ {stage['name']} 测试失败 (返回码: {result.returncode})")
                return False
                
        except Exception as e:
            logger.error(f"❌ {stage['name']} 测试异常: {e}")
            return False
    
    def get_user_confirmation(self, stage_name):
        """获取用户确认"""
        while True:
            try:
                response = input(f"\n{stage_name} 测试完成。请确认功能正常，是否继续下一阶段？(y/n/s): ").strip().lower()
                
                if response == 'y':
                    return True
                elif response == 'n':
                    logger.info("用户选择停止测试")
                    return False
                elif response == 's':
                    logger.info("用户选择跳过当前阶段")
                    return 'skip'
                else:
                    print("请输入 y (继续), n (停止), 或 s (跳过)")
                    
            except KeyboardInterrupt:
                logger.info("\n用户中断测试")
                return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始 Rhasspy3 系统性测试")
        logger.info(f"测试项目: {len(self.test_stages)} 个阶段")
        
        # 检查环境
        if not self.check_environment():
            logger.error("❌ 环境检查失败，无法继续测试")
            return False
        
        # 逐阶段执行测试
        for i, stage in enumerate(self.test_stages):
            logger.info(f"\n📍 当前进度: {i+1}/{len(self.test_stages)}")
            
            # 执行测试
            test_result = self.run_test_stage(stage)
            self.test_results[stage['name']] = test_result
            
            # 如果是必需的测试且失败了
            if stage['required'] and not test_result:
                logger.error(f"❌ 必需测试 {stage['name']} 失败")
                
                # 询问用户是否继续
                user_choice = self.get_user_confirmation(stage['name'])
                if user_choice == False:
                    break
                elif user_choice == 'skip':
                    logger.warning(f"⚠️  跳过 {stage['name']} 测试")
                    continue
            
            # 如果不是最后一个测试，获取用户确认
            if i < len(self.test_stages) - 1:
                user_choice = self.get_user_confirmation(stage['name'])
                if user_choice == False:
                    break
                elif user_choice == 'skip':
                    continue
        
        # 汇总测试结果
        self.print_test_summary()
        
        # 判断整体测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        if passed_tests >= total_tests * 0.75:  # 75% 通过率
            logger.info("🎉 系统性测试基本通过！")
            return True
        else:
            logger.warning("⚠️  系统性测试通过率较低，请检查配置")
            return False
    
    def print_test_summary(self):
        """打印测试汇总"""
        logger.info("=" * 80)
        logger.info("📊 Rhasspy3 系统性测试结果汇总")
        logger.info("=" * 80)
        
        for stage in self.test_stages:
            stage_name = stage['name']
            if stage_name in self.test_results:
                result = self.test_results[stage_name]
                status = "✅ 通过" if result else "❌ 失败"
                required = "必需" if stage['required'] else "可选"
                logger.info(f"   {stage_name:12} ({required}): {status} - {stage['description']}")
            else:
                logger.info(f"   {stage_name:12} (跳过): ⏭️  未执行 - {stage['description']}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\n总计: {passed_tests}/{total_tests} 项测试通过 (通过率: {pass_rate:.1f}%)")
        
        # 给出建议
        if pass_rate >= 90:
            logger.info("🌟 优秀！系统运行状态良好")
        elif pass_rate >= 75:
            logger.info("👍 良好！系统基本功能正常")
        elif pass_rate >= 50:
            logger.info("⚠️  一般，部分功能需要优化")
        else:
            logger.info("🔧 需要修复，系统存在较多问题")

def main():
    """主函数"""
    print("🎤 Rhasspy3 智能语音助手系统性测试")
    print("=" * 60)
    print("本测试将按照语音处理管道顺序验证各个组件功能")
    print("测试过程中需要您的语音输入和确认")
    print("=" * 60)
    
    # 确认开始测试
    try:
        response = input("是否开始测试？(y/n): ").strip().lower()
        if response != 'y':
            print("测试已取消")
            return 0
    except KeyboardInterrupt:
        print("\n测试已取消")
        return 0
    
    # 运行测试
    runner = TestRunner()
    success = runner.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Rhasspy3 系统性测试完成！")
        print("您的智能语音助手已准备就绪 🎉")
    else:
        print("❌ 测试过程中发现问题，请检查配置")
        print("建议查看日志文件: test/test_results.log")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
