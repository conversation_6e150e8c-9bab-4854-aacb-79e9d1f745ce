{% extends "layout.html" %}

{% block body %}

<h1>Speech to Text (asr)</h1>

<h2>Transcribe</h2>

<ol>
  <li>
    Pipeline:
    <select id="pipeline_name">
      <option value="default">default</option>
      {% set pipelines = config.pipelines | sort %}
      {% for pipeline in pipelines: %}
      {% if pipeline != "default": %}
      <option>{{ pipeline }}</option>
      {% endif %}
      {% endfor %}
    </select>
  </li>
  <li>
    Mic Program:
    <select id="asr_mic_program" onchange="asr_mic_change()">
      <option value="">Browser</option>
      <option value="">WAV file</option>
      <option value="">default</option>
      {% set programs = config.programs["mic"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li id="asr_wav_li" hidden>
    WAV file: <input id="asr_wav" type="file">
  </li>
  <li id="asr_vad_li">
    VAD Program:
    <select id="asr_vad_program">
      <option value="">default</option>
      {% set programs = config.programs["vad"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    ASR Program:
    <select id="asr_program">
      <option value="">default</option>
      {% set programs = config.programs["asr"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    <button onclick="asr_transcribe()">Transcribe</button>
  </li>
  <li>
    Status: <span id="asr_status"></span>
  </li>
  <li>
    Transcript: <span id="asr_transcript"></span>
  </li>
</ol>

<script type="text/javascript">

function asr_mic_change() {
    const micIndex = q("#asr_mic_program").selectedIndex;
    q("#asr_wav_li").hidden = (micIndex != 1);
    q("#asr_vad_li").hidden = (micIndex == 1);
}

function asr_transcribe() {
    const micIndex = q("#asr_mic_program").selectedIndex;
    if (micIndex == 0) {
        asr_transcribe_stream();
        return;
    }

    if (micIndex == 1) {
        asr_transcribe_wav();
        return;
    }

    const pipelineName = q("#pipeline_name").value;
    const micProgram = q("#asr_mic_program").value;
    const asrProgram = q("#asr_program").value;
    const vadProgram = q("#asr_vad_program").value;

    const status = q("#asr_status");
    status.innerText = "Listening";

    const transcript = q("#asr_transcript");
    transcript.innerText = "";

    const startTime = performance.now();
    fetch("{{ url_prefix }}" + "pipeline/run?start_after=wake&stop_after=asr"
          + "&pipeline=" + encodeURIComponent(pipelineName)
          + "&asr_program=" + encodeURIComponent(asrProgram)
          + "&mic_program=" + encodeURIComponent(micProgram)
          + "&vad_program=" + encodeURIComponent(vadProgram),
          {method: "POST"})
        .then(response => {
            const endTime = performance.now();
            status.innerText = "Done in " + (endTime - startTime) / 1000 + " second(s)";
            return response;
        })
        .then(response => response.json())
        .then(response => response.asr_transcript.data.text)
        .then(text => {
            transcript.innerText = text;
        })
        .catch(error => alert(error));
}

function asr_transcribe_wav() {
    const pipelineName = q("#pipeline_name").value;
    const asrProgram = q("#asr_program").value;

    const status = q("#asr_status");
    status.innerText = "Loading";

    const transcript = q("#asr_transcript");
    transcript.innerText = "";

    const files = q("#asr_wav").files;
    if (files.length < 1) {
        alert("No file");
        return;
    }

    const reader = new FileReader();
    reader.onload = function() {
        const wavData = this.result;
        status.innerText = "Transcribing";

        const startTime = performance.now();
        fetch("{{ url_prefix }}" + "asr/transcribe"
              + "&pipeline=" + encodeURIComponent(pipelineName)
              + "&asr_program=" + encodeURIComponent(asrProgram),
              { method: "POST", body: wavData })
            .then(response => {
                const endTime = performance.now();
                status.innerText = "Done in " + (endTime - startTime) / 1000 + " second(s)";
                return response;
            })
            .then(response => response.json())
            .then(response => response.data.text)
            .then(text => {
                transcript.innerText = text;
            })
            .catch(error => alert(error));
    }

    reader.readAsArrayBuffer(files[0]);
}

async function asr_transcribe_stream() {
    const pipelineName = q("#pipeline_name").value;
    const asrProgram = q("#asr_program").value;
    const vadProgram = q("#asr_vad_program").value;

    const status = q("#asr_status");
    status.innerText = "Connecting";

    const transcript = q("#asr_transcript");
    transcript.innerText = "";

    const context = new (window.AudioContext || window.webkitAudioContext)();
    const stream = await navigator.mediaDevices.getUserMedia({audio: true});
    await context.audioWorklet.addModule("/js/recorder.worklet.js");

    const source = context.createMediaStreamSource(stream);
    const recorder = new AudioWorkletNode(context, "recorder.worklet");

    const websocket = new WebSocket("ws://" + location.host + "/asr/transcribe"
                                    + "?rate=" + encodeURIComponent(context.sampleRate)
                                    + "&pipeline=" + encodeURIComponent(pipelineName)
                                    + "&asr_program=" + encodeURIComponent(asrProgram)
                                    + "&vad_program=" + encodeURIComponent(vadProgram));

    websocket.binaryType = "arraybuffer";
    websocket.onopen = function() {
        status.innerText = "Connected";

        source.connect(recorder).connect(context.destination);
        recorder.port.onmessage = function(e) {
            websocket.send(e.data);
        };
    };

    let audioArrayBuffers = [];
    let numAudioBytes = 0;
    websocket.onmessage = function(e) {
        source.disconnect();
        status.innerText = "Done";
        transcript.innerText = JSON.parse(e.data).data.text;
    };

}

</script>



{% endblock %}
