{% extends "layout.html" %}

{% block body %}

<h1>Satellite</h1>

<ol>
  <li>
    Pipeline:
    <select id="pipeline_name">
      <option value="default">default</option>
      {% set pipelines = config.pipelines | sort %}
      {% for pipeline in pipelines: %}
      {% if pipeline != "default": %}
      <option>{{ pipeline }}</option>
      {% endif %}
      {% endfor %}
    </select>
  </li>
  <li>
    ASR Program:
    <select id="pipeline_asr_program">
      <option value="">default</option>
      {% set programs = config.programs["asr"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    VAD Program:
    <select id="pipeline_vad_program">
      <option value="">default</option>
      {% set programs = config.programs["vad"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    Handle Program:
    <select id="pipeline_handle_program">
      <option value="">default</option>
      {% set programs = config.programs["handle"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    TTS Program:
    <select id="pipeline_tts_program">
      <option value="">default</option>
      {% set programs = config.programs["tts"].items() | sort %}
      {% for name, program in programs: %}
      {% if program.installed: %}
      <option>{{ name }}</option>
      {% endif %}
      {% endfor %}
    </select>
    </select>
  </li>
  <li>
    <button onclick="asr_tts()">Run</button>
  </li>
  <li>
    Status: <span id="pipeline_status"></span>
  </li>
  <li>
    Events:
    <ul id="events">
    </ul>
  </li>
  <li>
    <audio id="tts_audio" preload="none" controls autoplay></audio>
  </li>
</ol>

<script type="text/javascript">

async function asr_tts() {
    const pipelineName = q("#pipeline_name").value;
    const asrProgram = q("#pipeline_asr_program").value;
    const vadProgram = q("#pipeline_vad_program").value;
    const handleProgram = q("#pipeline_handle_program").value;
    const ttsProgram = q("#pipeline_tts_program").value;

    const status = q("#pipeline_status");
    status.innerText = "Connecting";

    const eventsList = q("#events");
    eventsList.innerHTML = "";

    const context = new (window.AudioContext || window.webkitAudioContext)();
    const stream = await navigator.mediaDevices.getUserMedia({audio: true});
    await context.audioWorklet.addModule("/js/recorder.worklet.js");

    const source = context.createMediaStreamSource(stream);
    const recorder = new AudioWorkletNode(context, "recorder.worklet");

    const websocket = new WebSocket("ws://" + location.host + "/pipeline/asr-tts"
                                    + "?in_rate=" + encodeURIComponent(context.sampleRate)
                                    + "&out_rate=" + encodeURIComponent(22050)
                                    + "&pipeline=" + encodeURIComponent(pipelineName)
                                    + "&asr_program=" + encodeURIComponent(asrProgram)
                                    + "&vad_program=" + encodeURIComponent(vadProgram)
                                    + "&handle_program=" + encodeURIComponent(handleProgram)
                                    + "&tts_program=" + encodeURIComponent(ttsProgram));

    websocket.binaryType = "arraybuffer";
    websocket.onopen = function() {
        status.innerText = "Connected";

        source.connect(recorder).connect(context.destination);
        recorder.port.onmessage = function(e) {
            websocket.send(e.data);
        };
    };

    let audioArrayBuffers = [];
    let numAudioBytes = 0;
    websocket.onmessage = function(e) {
        if (typeof e.data === "string") {
            eventsList.innerHTML += "<li>" + e.data + "</li>";
        } else {
            audioArrayBuffers.push(e.data);
            numAudioBytes += e.data.byteLength;
        }
    };

    websocket.onclose = function() {
        // Stop streaming audio
        source.disconnect();
        
        const audioBytes = new Uint8Array(numAudioBytes);

        // Copy to single buffer
        let audioBytesIndex = 0;
        for (let i = 0; i < audioArrayBuffers.length; i++) {
            const buffer = audioArrayBuffers[i];
            audioBytes.set(new Uint8Array(buffer), audioBytesIndex);
            audioBytesIndex += buffer.byteLength;
        }
        
        const wavHeader = new Uint8Array(buildWaveHeader({
            numFrames: audioBytes.byteLength / 2,
            bytesPerSample: 2,
            sampleRate: 22050,
            numChannels: 1,
            format: 1
        }))

        // create WAV file with header and downloaded PCM audio
        const wavBytes = new Uint8Array(wavHeader.length + audioBytes.byteLength)
        wavBytes.set(wavHeader, 0)
        wavBytes.set(audioBytes, wavHeader.length)
        const blob = new Blob([wavBytes], { "type": "audio/wav" });

        q("#tts_audio").src = window.URL.createObjectURL(blob);
        status.innerText = "Done";
    };

}

</script>



{% endblock %}
