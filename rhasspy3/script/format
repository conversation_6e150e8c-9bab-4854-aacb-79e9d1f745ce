#!/usr/bin/env bash
set -eo pipefail

# Directory of *this* script
this_dir="$( cd "$( dirname "$0" )" && pwd )"

base_dir="$(realpath "${this_dir}/..")"

# Path to virtual environment
: "${venv:=${base_dir}/.venv}"

if [ -d "${venv}" ]; then
    # Activate virtual environment if available
    source "${venv}/bin/activate"
fi

python_files=()
python_files+=("${base_dir}/bin")
python_files+=("${base_dir}/rhasspy3")
python_files+=("${base_dir}/rhasspy3_http_api")
python_files+=("${base_dir}/programs")

# Format code
black "${python_files[@]}"
isort "${python_files[@]}"
