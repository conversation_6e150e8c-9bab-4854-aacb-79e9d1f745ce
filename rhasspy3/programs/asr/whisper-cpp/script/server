#!/usr/bin/env bash
set -eo pipefail

# Directory of *this* script
this_dir="$( cd "$( dirname "$0" )" && pwd )"

# Base directory of repo
base_dir="$(realpath "${this_dir}/..")"

# Path to virtual environment
: "${venv:=${base_dir}/.venv}"

if [ -d "${venv}" ]; then
    source "${venv}/bin/activate"
fi

socket_dir="${base_dir}/var/run"
mkdir -p "${socket_dir}"

lib_dir="${base_dir}/lib"
export LD_LIBRARY_PATH="${lib_dir}:${LD_LIBRARY_PATH}"
export PYTHONPATH="${lib_dir}:${PYTHONPATH}"

python3 "${base_dir}/bin/whisper_cpp_server.py" --socketfile "${socket_dir}/whisper-cpp.socket" "$@"
