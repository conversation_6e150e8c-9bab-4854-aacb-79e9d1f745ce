# Aibi-Rhasspy 端到端集成使用指南

## 🎯 **概述**

本指南介绍如何使用 Aibi-Rhasspy 项目的端到端集成功能，基于 Rhasspy 3.0 架构实现完整的语音交互流程。

### **🔄 完整语音交互流程**

```
音频输入 → 唤醒词检测 → ASR语音识别 → 意图理解 → TTS语音合成 → 音频输出
    ↓           ↓            ↓           ↓           ↓           ↓
  麦克风    Hey, 艾比    SenseVoice    Dify AI    CosyVoice    扬声器
```

## 🚀 **快速开始**

### **1. 环境检查**

首先检查开发环境是否就绪：

```bash
# 激活 conda 环境
conda activate aibi

# 检查环境
./scripts/dev.sh check
```

### **2. 启动开发环境**

```bash
# 一键启动 Rhasspy 服务器
./scripts/dev.sh start
```

启动成功后，您将看到：
- ✅ Rhasspy 服务器启动成功 (PID: xxxx)
- 🌐 API 地址: http://localhost:13331

### **3. 运行端到端测试**

```bash
# 运行完整的集成测试
./scripts/dev.sh test
```

### **4. 启动语音助手**

```bash
# 启动交互式语音助手
./scripts/dev.sh voice
```

## 🧪 **测试功能**

### **端到端集成测试 (tests/aibi.py)**

这个测试脚本验证完整的语音交互管道：

#### **测试步骤**

1. **环境检查** - 验证 Conda 环境、音频设备、模型文件
2. **启动 Rhasspy 服务器** - 自动启动 HTTP API 服务
3. **唤醒词检测** - 测试 "Hey, 艾比" 检测
4. **语音识别** - 测试中文语音转文本
5. **意图理解** - 测试 Dify AI 意图识别
6. **意图处理** - 测试智能回复生成
7. **语音合成** - 测试 CosyVoice TTS
8. **音频播放** - 测试音频输出
9. **完整管道** - 测试端到端流程

#### **运行单个测试**

```bash
# 测试环境
python tests/aibi.py --step env

# 测试唤醒词
python tests/aibi.py --step wake

# 测试语音识别
python tests/aibi.py --step asr

# 测试完整管道
python tests/aibi.py --step pipeline
```

#### **调试模式**

```bash
# 启用详细日志
python tests/aibi.py --debug
```

## 🛠️ **开发脚本 (scripts/dev.sh)**

### **主要功能**

| 命令 | 功能 | 说明 |
|------|------|------|
| `./scripts/dev.sh start` | 启动服务器 | 启动 Rhasspy HTTP API 服务器 |
| `./scripts/dev.sh stop` | 停止服务器 | 停止所有相关服务 |
| `./scripts/dev.sh restart` | 重启服务器 | 重启 Rhasspy 服务器 |
| `./scripts/dev.sh status` | 检查状态 | 查看服务器运行状态 |
| `./scripts/dev.sh test` | 运行测试 | 执行端到端集成测试 |
| `./scripts/dev.sh voice` | 语音助手 | 启动交互式语音助手 |
| `./scripts/dev.sh api` | API 示例 | 显示 API 测试命令 |
| `./scripts/dev.sh logs` | 查看日志 | 实时查看服务器日志 |
| `./scripts/dev.sh check` | 环境检查 | 检查开发环境 |
| `./scripts/dev.sh cleanup` | 清理环境 | 清理临时文件和进程 |

### **交互式菜单**

```bash
# 启动交互式菜单
./scripts/dev.sh
```

## 🌐 **API 测试**

### **Rhasspy 3.0 HTTP API**

服务器启动后，可以通过 HTTP API 测试各个组件：

#### **1. 检查版本**

```bash
curl http://localhost:13331/version
```

#### **2. 测试语音合成**

```bash
curl -X POST http://localhost:13331/tts/synthesize \
  -d '你好，我是艾比智能助手' \
  -H 'Content-Type: text/plain' \
  --output test.wav
```

#### **3. 测试意图识别**

```bash
curl -X POST http://localhost:13331/intent/recognize \
  -d '今天天气怎么样' \
  -H 'Content-Type: text/plain'
```

#### **4. 测试完整管道**

```bash
curl -X POST http://localhost:13331/pipeline/run \
  -H 'Content-Type: application/json' \
  -d '{"pipeline": "default"}'
```

## 🎤 **语音交互使用**

### **启动语音助手**

```bash
./scripts/dev.sh voice
```

### **交互流程**

1. **等待唤醒** - 程序会监听麦克风，等待 "Hey, 艾比" 唤醒词
2. **语音指令** - 检测到唤醒词后，说出您的指令
3. **AI 处理** - 系统会识别语音、理解意图、生成回复
4. **语音回复** - 通过扬声器播放 AI 的语音回复

### **示例对话**

```
用户: "Hey, 艾比"
系统: [检测到唤醒词]

用户: "今天天气怎么样？"
系统: "今天天气晴朗，温度适宜，适合外出活动。"

用户: "帮我设置一个提醒"
系统: "好的，请告诉我提醒的内容和时间。"
```

## 🔧 **配置说明**

### **主配置文件 (config/configuration.yaml)**

基于 Rhasspy 3.0 标准，配置了以下组件：

- **mic**: 音频输入 (arecord/sounddevice)
- **wake**: 唤醒词检测 (hey-aibi)
- **asr**: 语音识别 (sensevoice)
- **intent**: 意图理解 (dify)
- **handle**: 意图处理 (dify-extract)
- **tts**: 语音合成 (cosyvoice)
- **snd**: 音频输出 (aplay)

### **管道配置**

```yaml
pipelines:
  default:
    mic: { name: arecord }
    wake: { name: hey-aibi }
    asr: { name: sensevoice }
    intent: { name: dify }
    handle: { name: dify-extract }
    tts: { name: cosyvoice-clone }
    snd: { name: aplay }
```

## 📊 **测试报告**

测试完成后，会生成详细的测试报告：

- **位置**: `logs/aibi_e2e_test_YYYYMMDD_HHMMSS.json`
- **内容**: 包含每个测试步骤的结果、耗时、错误信息
- **格式**: JSON 格式，便于分析和调试

### **报告示例**

```json
{
  "timestamp": "2025-08-02T16:30:00",
  "summary": {
    "total_tests": 9,
    "passed_tests": 8,
    "failed_tests": 1,
    "success_rate": 88.9,
    "total_duration": 45.2
  },
  "results": {
    "环境检查": {"success": true, "duration": 2.1},
    "唤醒词检测": {"success": true, "duration": 3.5},
    "语音识别": {"success": true, "duration": 4.2}
  }
}
```

## 🚨 **故障排除**

### **常见问题**

#### **1. 服务器启动失败**

```bash
# 检查端口占用
lsof -i :13331

# 检查日志
tail -f logs/rhasspy_dev_*.log
```

#### **2. 音频设备问题**

```bash
# 检查录音设备
arecord -l

# 检查播放设备
aplay -l

# 测试录音
arecord -d 3 test.wav && aplay test.wav
```

#### **3. 模型文件缺失**

```bash
# 检查模型文件
ls -la config/programs/*/models/
```

#### **4. Conda 环境问题**

```bash
# 重新激活环境
conda deactivate
conda activate aibi

# 检查环境
conda info --envs
```

### **日志查看**

```bash
# 查看实时日志
./scripts/dev.sh logs

# 查看特定日志文件
tail -f logs/rhasspy_dev_20250802_160000.log
```

## 🎯 **最佳实践**

### **开发流程**

1. **环境检查** - 每次开发前先运行 `./scripts/dev.sh check`
2. **启动服务** - 使用 `./scripts/dev.sh start` 启动开发环境
3. **功能测试** - 使用 `./scripts/dev.sh test` 验证功能
4. **交互测试** - 使用 `./scripts/dev.sh voice` 进行真实交互
5. **清理环境** - 开发完成后运行 `./scripts/dev.sh cleanup`

### **调试技巧**

1. **启用调试模式** - 使用 `--debug` 参数获取详细日志
2. **分步测试** - 使用 `--step` 参数测试单个组件
3. **查看日志** - 实时监控日志文件排查问题
4. **API 测试** - 使用 curl 命令直接测试 API 接口

## 🎉 **成功验证**

当您看到以下输出时，说明系统运行正常：

```
✅ Rhasspy 服务器启动成功
🌐 API 地址: http://localhost:13331
🎯 系统就绪，等待语音指令...
💡 提示: 说 'Hey, 艾比' 来唤醒助手
```

现在您可以享受完整的语音交互体验了！🎊
