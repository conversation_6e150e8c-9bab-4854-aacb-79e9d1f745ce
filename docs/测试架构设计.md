# 🧪 测试架构设计

## 📋 **设计原则**

### **🎯 核心原则**
1. **测试与生产分离** - 测试代码与生产代码完全分离
2. **模块化测试** - 按功能模块组织测试
3. **分层测试** - 单元测试 → 模块测试 → 集成测试
4. **自动化执行** - 支持单独和批量测试执行
5. **标准化报告** - 统一的测试结果格式和报告

### **🏗️ 架构分层**
```
测试架构
├── 单元测试层 (Unit Tests)
│   ├── 模型文件测试
│   ├── 配置文件测试
│   └── 程序功能测试
├── 模块测试层 (Module Tests)
│   ├── 唤醒词模块测试
│   ├── ASR 模块测试
│   └── TTS 模块测试
├── 集成测试层 (Integration Tests)
│   ├── 管道集成测试
│   ├── 性能集成测试
│   └── 端到端测试
└── 系统测试层 (System Tests)
    ├── 完整流程测试
    ├── 压力测试
    └── 兼容性测试
```

## 📁 **目录结构**

### **🗂️ 测试目录组织**
```
tests/
├── utils/                          # 测试工具模块
│   ├── test_helpers.py             # 通用测试工具类
│   ├── audio_utils.py              # 音频测试工具
│   └── mock_data.py                # 模拟数据生成
├── wake/                           # 唤醒词模块测试
│   ├── test_hey_aibi.py            # Hey, 艾比 唤醒词测试
│   ├── test_mfcc_extractor.py      # MFCC 特征提取测试
│   └── test_wake_detector.py       # 唤醒词检测器测试
├── asr/                            # ASR 模块测试
│   ├── test_sensevoice.py          # SenseVoiceSmall 测试
│   ├── test_audio_processor.py     # 音频处理器测试
│   └── test_accuracy.py           # 识别准确率测试
├── tts/                            # TTS 模块测试（未来）
│   └── test_cosyvoice.py           # CosyVoice 测试
├── integration/                    # 集成测试
│   ├── test_wake_asr_pipeline.py   # 唤醒词+ASR 管道测试
│   ├── test_full_pipeline.py       # 完整管道测试
│   └── test_rhasspy_integration.py # Rhasspy 集成测试
├── config/                         # 配置测试
│   └── test_configuration.py       # 配置文件测试
└── main_test.py                    # 主测试脚本
```

### **🔧 生产脚本目录（scripts/）**
```
scripts/
├── setup_project.sh               # 项目初始化脚本
├── setup_symlinks.sh              # 符号链接设置脚本
├── start_rhasspy.sh               # Rhasspy 启动脚本
├── list_programs.py               # 程序发现工具
└── verify_conda_setup.py          # 环境验证脚本
```

## 🛠️ **测试工具框架**

### **📦 核心工具类**

#### **TestResult 类**
```python
class TestResult:
    def __init__(self, success: bool, message: str = "", data: Dict = None)
    # 标准化的测试结果表示
```

#### **TestReporter 类**
```python
class TestReporter:
    def __init__(self, test_name: str)
    def add_result(self, result: TestResult)
    def print_report(self)
    # 统一的测试报告生成
```

#### **AudioTestUtils 类**
```python
class AudioTestUtils:
    @staticmethod
    def create_test_audio(text: str, duration: float = 2.0) -> np.ndarray
    @staticmethod
    def create_wake_audio(wake_word: str, command: str = "") -> np.ndarray
    # 音频测试数据生成工具
```

#### **ProcessTestUtils 类**
```python
class ProcessTestUtils:
    @staticmethod
    def run_command(cmd: List[str], timeout: int = 30) -> Tuple[bool, str, str]
    @staticmethod
    def extract_json_from_output(output: str) -> Dict
    # 进程执行和输出解析工具
```

### **📊 测试数据管理**

#### **标准测试用例**
```python
TEST_CASES = {
    "wake_words": [
        {"text": "hey_aibi", "description": "标准唤醒词"},
        {"text": "hey aibi", "description": "带空格唤醒词"},
    ],
    "asr_commands": [
        {"text": "你好世界", "description": "中文问候", "language": "zh"},
        {"text": "Hello world", "description": "英文问候", "language": "en"},
        {"text": "打开客厅的灯", "description": "智能家居指令", "language": "zh"},
    ],
    "pipeline_scenarios": [
        {
            "wake_word": "hey_aibi",
            "command": "打开客厅的灯",
            "description": "智能家居控制",
            "expected_intent": "turn_on_light"
        }
    ]
}
```

#### **测试配置**
```python
TEST_CONFIG = {
    "audio": {
        "sample_rate": 16000,
        "duration": 2.0,
        "channels": 1
    },
    "timeouts": {
        "asr": 60,
        "wake": 10,
        "pipeline": 120
    },
    "thresholds": {
        "wake_confidence": 0.85,
        "realtime_factor": 1.0
    },
    "paths": {
        "wake_model": "models/kws/hey_aibi.onnx",
        "asr_model": "models/asr/SenseVoiceSmall",
        "config_file": "config/configuration.yaml"
    }
}
```

## 🔄 **测试执行流程**

### **🎯 单模块测试流程**
1. **前置检查** - 检查模型文件、程序文件是否存在
2. **功能测试** - 测试基本功能是否正常
3. **性能测试** - 测试响应时间和实时性
4. **错误处理测试** - 测试异常情况处理
5. **结果报告** - 生成详细的测试报告

### **🔗 集成测试流程**
1. **依赖检查** - 确保所有依赖模块可用
2. **管道测试** - 测试模块间的协作
3. **端到端测试** - 测试完整的用户场景
4. **性能测试** - 测试整体系统性能
5. **压力测试** - 测试系统稳定性

### **📊 主测试流程**
```bash
# 运行所有测试
python tests/main_test.py --all

# 运行特定模块测试
python tests/main_test.py --modules config wake asr

# 运行单个模块测试
python tests/wake/test_hey_aibi.py
python tests/asr/test_sensevoice.py
python tests/integration/test_wake_asr_pipeline.py
```

## 📈 **测试策略**

### **🎯 测试优先级**
1. **P0 - 核心功能** - 配置文件、模型加载、基本识别
2. **P1 - 性能指标** - 实时性、准确率、稳定性
3. **P2 - 集成功能** - 模块协作、管道流程
4. **P3 - 边界情况** - 错误处理、异常恢复

### **📊 测试覆盖率目标**
- **配置测试**: 100% 覆盖所有配置项
- **功能测试**: 90% 覆盖核心功能路径
- **集成测试**: 80% 覆盖主要使用场景
- **性能测试**: 100% 覆盖关键性能指标

### **🔄 测试执行策略**
- **开发阶段**: 单模块测试 → 快速反馈
- **集成阶段**: 集成测试 → 验证协作
- **发布前**: 全量测试 → 确保质量
- **生产监控**: 健康检查 → 持续验证

## 🎯 **质量标准**

### **✅ 通过标准**
- **单模块测试**: 90% 以上测试通过
- **集成测试**: 80% 以上测试通过
- **性能测试**: 满足实时性要求（RTF < 1.0）
- **稳定性测试**: 连续运行无崩溃

### **📊 性能基准**
- **唤醒词检测**: < 2s 响应时间
- **ASR 识别**: RTF < 1.0（实时因子）
- **管道延迟**: < 30s 端到端处理
- **内存使用**: < 2GB 峰值内存

### **🛡️ 可靠性要求**
- **错误恢复**: 优雅处理异常情况
- **资源管理**: 无内存泄漏
- **并发安全**: 支持多请求处理
- **数据完整性**: 确保处理结果正确

## 🚀 **使用指南**

### **🔧 开发者使用**
```bash
# 开发新功能后，运行相关模块测试
python tests/wake/test_hey_aibi.py

# 修改配置后，运行配置测试
python tests/config/test_configuration.py

# 完成开发后，运行完整测试
python tests/main_test.py --all
```

### **🎯 CI/CD 集成**
```bash
# 持续集成脚本
#!/bin/bash
conda activate aibi
python tests/main_test.py --all --stop-on-failure
```

### **📊 测试报告**
- **控制台输出**: 实时测试进度和结果
- **详细报告**: 包含所有测试项的详细信息
- **统计信息**: 通过率、耗时、性能指标
- **失败分析**: 失败原因和建议修复方案

## 🎉 **架构优势**

### **✅ 设计优势**
1. **清晰分离** - 测试和生产代码完全分离
2. **模块化** - 支持独立的模块测试
3. **可扩展** - 易于添加新的测试模块
4. **标准化** - 统一的测试框架和报告格式
5. **自动化** - 支持批量执行和 CI/CD 集成

### **🔧 维护优势**
1. **易于调试** - 清晰的错误信息和日志
2. **快速定位** - 模块化测试快速定位问题
3. **持续验证** - 自动化测试确保质量
4. **文档完整** - 详细的测试文档和使用指南

---

**这个测试架构设计解决了测试与生产代码混合的问题，建立了标准化、模块化、自动化的测试体系，为项目的持续开发和质量保证提供了坚实的基础。**
