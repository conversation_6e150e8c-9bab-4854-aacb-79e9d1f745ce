# 🎯 Hey, 艾比 唤醒词使用指南

## 🚀 **快速开始**

### **1. 激活环境**
```bash
conda activate aibi
```

### **2. 进入程序目录**
```bash
cd config/programs/wake/hey-aibi
```

### **3. 运行唤醒词检测**

#### **方式一：持续检测（推荐）**
```bash
# 快速启动持续检测
./scripts/quick_mic_test.sh

# 或使用 Python 脚本（更详细的输出）
python3 scripts/continuous_wake_test.py

# 指定不同阈值
./scripts/quick_mic_test.sh 0.7
python3 scripts/continuous_wake_test.py --threshold 0.7
```

#### **方式二：手动启动**
```bash
# 进入程序目录
cd config/programs/wake/hey-aibi

# 使用 ALSA 音频输入
arecord -r 16000 -c 1 -f S16_LE -t raw | .venv/bin/python3 bin/hey_aibi_raw_text.py --model share/hey_aibi.onnx --debug
```

## ⚙️ **配置参数**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--model` | `share/hey_aibi.onnx` | ONNX 模型路径 |
| `--threshold` | `0.85` | 检测阈值 (0-1) |
| `--wake-word` | `hey_aibi` | 唤醒词名称 |
| `--samples-per-chunk` | `1024` | 音频块大小 |
| `--debug` | `False` | 调试模式 |

## 🎵 **音频输入方式**

### **方式一：ALSA（推荐生产环境）**
```bash
# 优势：稳定、轻量、系统标准
arecord -r 16000 -c 1 -f S16_LE -t raw | python3 bin/hey_aibi_raw_text.py
```

### **方式二：SoundDevice（推荐开发测试）**
```bash
# 优势：跨平台、灵活、实时性好
# 需要在 Rhasspy 3.0 中配置 sounddevice 程序
```

## 🔧 **Rhasspy 3.0 集成**

在 `config/configuration.yaml` 中配置：

```yaml
pipelines:
  main:
    mic:
      name: arecord        # 或 sounddevice
    wake:
      name: hey-aibi
```

## 📊 **性能指标**

- ⚡ **检测延迟**: < 100ms
- 🎯 **准确率**: > 95%
- 💾 **内存占用**: < 50MB
- 🔄 **实时处理**: 支持

## 🛠️ **故障排除**

### **音频设备问题**
```bash
# 检查音频设备
arecord -l
aplay -l

# 测试录音
arecord -r 16000 -c 1 -f S16_LE -d 3 test.wav
```

### **模型加载问题**
```bash
# 检查模型文件
ls -la share/hey_aibi.onnx

# 检查依赖
.venv/bin/python3 -c "import onnxruntime; print('OK')"
```

### **权限问题**
```bash
# 添加用户到音频组
sudo usermod -a -G audio $USER
```

## 🎯 **下一步**

唤醒词检测已就绪，可以继续集成：
1. **SenseVoiceSmall ASR** - 语音识别
2. **CosyVoice-300M TTS** - 语音合成
3. **Dify 平台** - 智能对话
