# 🛠️ Aibi 语音助手开发环境使用指南

## 🎯 **开发环境概述**

开发环境已成功设置，systemd 服务已移除，现在提供专门的开发调试工具。

### **✅ 已完成的设置**

1. **移除 systemd 服务** - 不再作为后台服务运行
2. **创建开发脚本** - 提供详细的调试信息
3. **音频设备检测** - 自动检测和测试音频设备
4. **实时日志输出** - 前台运行，便于调试
5. **优雅退出支持** - Ctrl+C 安全停止

## 🚀 **快速启动**

### **方法 1：快速启动脚本（推荐）**

```bash
# 在项目根目录执行
cd /home/<USER>/project/Aibi-Rhasspy

# 运行快速启动脚本
./scripts/start_dev.sh
```

**启动选项：**
- **选项 1**: 启动原版语音助手 (完整功能)
- **选项 2**: 启动开发版语音助手 (详细调试信息)
- **选项 3**: 仅显示设备信息

### **方法 2：直接运行开发版**

```bash
# 激活环境并运行开发版
conda activate aibi
python scripts/dev_aibi_voice_assistant.py
```

### **方法 3：直接运行原版**

```bash
# 激活环境并运行原版
conda activate aibi
python scripts/aibi_voice_assistant.py
```

## 🔧 **开发工具功能**

### **1. 详细的音频设备信息**

开发脚本会自动显示：

```
🎤 当前音频设备:
录音设备:
  card 0: PCH [HDA Intel PCH], device 0: ALC897 Analog
  card 1: Gadget [USB Gadget], device 0: USB Audio

播放设备:
  card 0: PCH [HDA Intel PCH], device 0: ALC897 Analog
  card 2: NVidia [HDA NVidia], device 3: HDMI 0

🧪 快速测试录音设备...
✅ 录音设备工作正常
```

### **2. 实时状态监控**

开发版提供彩色日志输出：

```
🔍 检测音频设备...
✅ 录音设备检测成功
📱 发现 3 个录音设备
🎯 寻找最佳录音设备...
🎤 最佳录音设备: default
🤖 开始加载模型...
✅ 唤醒词模型: 260K 字节
✅ ASR模型: 897M 字节
✅ TTS模型: 5.4G 字节
```

### **3. 详细的录音信息**

每次录音都会显示详细信息：

```
🎤 开始录音 2s (设备: default, 采样率: 16000Hz)
✅ 录音完成: 64,044 字节, 实际时长: 2.01s
🔍 检测唤醒词...
```

### **4. 完整的交互流程日志**

```
👂 等待唤醒词 'Hey, 艾比'...
🎯 检测到唤醒词！
🗣️ 请说出您的指令...
🎤 开始录音 5s (设备: default, 采样率: 16000Hz)
✅ 录音完成: 160,044 字节, 实际时长: 5.00s
🤖 正在识别语音...
📝 识别结果: '你好艾比，今天天气怎么样？'
🧠 正在理解意图...
💭 AI 回复: '您好！今天天气晴朗，温度适宜...'
🎵 正在合成语音...
✅ 语音合成完成
🔊 正在播放回复...
✅ 播放完成
🎉 交互循环完成
```

## 📊 **音频设备配置**

### **当前检测到的设备**

#### **录音设备**
- **默认设备**: `default` ✅ 推荐使用
- **硬件设备**: `hw:CARD=PCH,DEV=0` (ALC897 Analog)
- **插件设备**: `plughw:CARD=PCH,DEV=0` (兼容性更好)
- **USB 设备**: `hw:CARD=Gadget,DEV=0` (USB Audio)

#### **播放设备**
- **默认设备**: `default`
- **模拟输出**: `hw:CARD=PCH,DEV=0` (ALC897 Analog)
- **数字输出**: `hw:CARD=PCH,DEV=1` (ALC897 Digital)
- **HDMI 输出**: `hw:CARD=NVidia,DEV=3` (HDMI 0)

### **设备测试结果**
- ✅ **录音设备**: `default` 工作正常
- ⚠️ **播放设备**: 部分设备可能需要配置

## 🔧 **开发调试功能**

### **1. 音频设备测试**

```bash
# 测试所有音频设备
./scripts/dev_voice_assistant.sh --test

# 查看设备信息
./scripts/dev_voice_assistant.sh --info
```

### **2. 模型文件检查**

开发脚本会自动检查所有模型文件：

```
🤖 模型文件信息
✅ 唤醒词模型: 存在 (260K)
✅ ASR模型: 存在 (897M)  
✅ TTS模型: 存在 (5.4G)
```

### **3. 系统信息显示**

```
💻 系统信息
🖥️ 操作系统: Ubuntu 22.04.5 LTS
🏗️ 架构: x86_64
💾 内存: 32GB
💿 磁盘空间: 1.6T 可用
🐍 Python: Python 3.10.18
```

## ⌨️ **控制命令**

### **运行时控制**
- **Ctrl+C**: 优雅退出程序
- **程序会自动清理临时文件**
- **支持信号处理，安全停止**

### **脚本参数**

```bash
# 显示帮助
./scripts/dev_voice_assistant.sh --help

# 仅显示系统信息
./scripts/dev_voice_assistant.sh --info

# 仅测试音频设备
./scripts/dev_voice_assistant.sh --test

# 快速启动（交互式选择）
./scripts/start_dev.sh
```

## 🐛 **调试技巧**

### **1. 查看详细日志**

开发版提供彩色日志，包含：
- 🔍 调试信息 (DEBUG)
- 📝 一般信息 (INFO)
- ⚠️ 警告信息 (WARNING)
- ❌ 错误信息 (ERROR)

### **2. 音频问题排查**

```bash
# 手动测试录音
arecord -D default -f S16_LE -c 1 -r 16000 -d 3 test.wav

# 手动测试播放
aplay test.wav

# 查看音频设备
arecord -l
aplay -l
```

### **3. 模型加载问题**

```bash
# 检查模型文件
ls -la config/programs/*/models/

# 检查 conda 环境
conda activate aibi
python -c "import torch; print(torch.__version__)"
```

### **4. 网络连接问题**

```bash
# 测试 Dify API 连接
curl -X POST "http://***********/..." --timeout 5
```

## 📋 **开发工作流程**

### **典型开发流程**

1. **启动开发环境**
   ```bash
   ./scripts/start_dev.sh
   ```

2. **选择开发版本** (选项 2)
   - 获得详细的调试信息
   - 实时查看每个步骤的执行情况

3. **测试语音交互**
   - 观察录音设备使用情况
   - 检查每个组件的响应时间
   - 查看错误信息和警告

4. **调试和优化**
   - 根据日志信息定位问题
   - 调整配置参数
   - 测试不同的音频设备

5. **验证修改**
   - 重启程序测试修改效果
   - 使用 Ctrl+C 安全退出

### **代码修改建议**

- **主程序**: `scripts/aibi_voice_assistant.py`
- **开发版**: `scripts/dev_aibi_voice_assistant.py`
- **配置文件**: `config/edge_config.yaml`
- **启动脚本**: `scripts/start_dev.sh`

## 🔄 **从开发环境到生产环境**

### **开发完成后部署**

1. **测试完成后**，可以重新安装 systemd 服务：
   ```bash
   ./scripts/install_edge_service.sh
   ```

2. **或者使用快速部署**：
   ```bash
   ./scripts/quick_deploy_edge.sh
   ```

### **配置同步**

开发环境的配置修改可以同步到生产环境：
- 复制 `config/edge_config.yaml` 的修改
- 更新模型路径和参数
- 同步音频设备配置

## 🎯 **开发环境优势**

### **✅ 相比 systemd 服务的优势**

1. **实时调试** - 立即看到日志输出
2. **快速迭代** - 修改代码后立即测试
3. **详细信息** - 完整的设备和状态信息
4. **灵活控制** - 随时启动停止
5. **错误定位** - 清晰的错误信息和堆栈跟踪

### **🎯 适用场景**

- **功能开发** - 添加新功能时
- **问题调试** - 排查系统问题时
- **性能优化** - 分析性能瓶颈时
- **设备测试** - 测试不同音频设备时
- **参数调优** - 调整模型参数时

---

## 🎉 **总结**

### **✅ 开发环境已就绪**

您现在拥有了完整的开发调试环境：

1. **🔧 专业的开发工具** - 详细的设备信息和状态监控
2. **🎤 音频设备支持** - 自动检测和测试音频设备
3. **📊 实时日志输出** - 彩色日志，便于调试
4. **⌨️ 灵活的控制方式** - 多种启动选项和控制命令
5. **🐛 完善的调试功能** - 错误定位和性能分析

### **🚀 立即开始开发**

```bash
# 快速启动开发环境
cd /home/<USER>/project/Aibi-Rhasspy
./scripts/start_dev.sh
```

**现在您可以方便地进行语音助手的开发和调试了！** 🎊
