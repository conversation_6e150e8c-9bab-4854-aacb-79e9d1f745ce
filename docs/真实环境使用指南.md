# 🎯 Aibi-Rhasspy 真实环境使用指南

## 📋 **系统状态检查**

### **✅ 已验证正常的功能**
- ✅ HTTP API 服务器运行正常
- ✅ Dify 意图识别和对话功能完全正常
- ✅ Web 管理界面可用
- ✅ 配置管理系统正常
- ✅ 程序发现和符号链接正常

### **⚠️ 需要完善的功能**
- ⚠️ 唤醒词模型需要训练或下载
- ⚠️ ASR 模型需要下载
- ⚠️ TTS 模型需要配置

## 🚀 **当前可用的功能**

### **1. 文本对话功能（完全可用）**

您现在就可以通过以下方式与艾比对话：

#### **Web 界面对话**
1. 访问 `http://localhost:13331/pipeline.html`
2. 在 Intent Program 选择 `dify`
3. 输入文本进行对话测试

#### **API 对话**
```bash
curl -X POST "http://localhost:13331/intent/recognize?intent_program=dify" \
  -H "Content-Type: text/plain" \
  -d "你好艾比，今天天气怎么样？"
```

#### **支持的对话类型**
- ✅ 日常问候："你好艾比"
- ✅ 天气查询："今天天气怎么样"
- ✅ 智能家居控制："打开客厅的灯"
- ✅ 娱乐功能："播放音乐"
- ✅ 知识问答：各种问题

### **2. 智能家居控制（通过 Dify 工作流）**

艾比可以理解和处理智能家居指令：
- "打开客厅的灯"
- "关闭空调"
- "调节温度到 25 度"
- "切换到回家模式"

## 🎤 **语音功能配置指南**

### **步骤 1：配置唤醒词（Hey, 艾比）**

1. **下载或训练唤醒词模型**：
   ```bash
   # 创建模型目录
   mkdir -p config/programs/wake/hey-aibi/models/kws
   
   # 如果您有训练好的模型，放置到：
   # config/programs/wake/hey-aibi/models/kws/hey_aibi.onnx
   ```

2. **测试唤醒词程序**：
   ```bash
   conda activate aibi
   python config/programs/wake/hey-aibi/bin/hey_aibi_raw_text.py --help
   ```

### **步骤 2：配置语音识别（SenseVoice）**

1. **下载 SenseVoiceSmall 模型**：
   ```bash
   # 创建模型目录
   mkdir -p config/programs/asr/sensevoice/models/asr
   
   # 下载模型（需要从官方渠道获取）
   # 模型应放置在：
   # config/programs/asr/sensevoice/models/asr/SenseVoiceSmall/
   ```

2. **测试 ASR 程序**：
   ```bash
   conda activate aibi
   python config/programs/asr/sensevoice/bin/sensevoice_wav2text.py --help
   ```

### **步骤 3：配置语音合成（CosyVoice）**

1. **下载 CosyVoice 模型**：
   ```bash
   # 创建模型目录
   mkdir -p config/programs/tts/cosyvoice/models
   
   # 下载 CosyVoice-300M 模型
   # 模型应放置在相应目录
   ```

2. **测试 TTS 程序**：
   ```bash
   conda activate aibi
   python config/programs/tts/cosyvoice/bin/cosyvoice_text2wav.py --help
   ```

## 🎯 **完整语音管道使用**

### **配置步骤**

1. **在 Web 界面配置管道**：
   - 访问 `http://localhost:13331/pipeline.html`
   - Pipeline: `default`
   - Wake Program: `hey-aibi`
   - ASR Program: `sensevoice`
   - Intent Program: `dify`
   - Handle Program: `dify-extract`
   - TTS Program: `cosyvoice-sft`

2. **启动语音管道**：
   - 点击 "Run" 按钮启动管道
   - 系统开始监听唤醒词

### **使用流程**

1. **唤醒**：说出 "Hey, 艾比"
2. **等待**：听到提示音后开始说话
3. **对话**：说出您的指令或问题
4. **响应**：艾比会语音回复

### **示例对话**

```
用户: "Hey, 艾比"
系统: [提示音]
用户: "今天天气怎么样？"
艾比: "今天天气晴朗，温度 25 度，适合外出活动。"

用户: "Hey, 艾比"
系统: [提示音]
用户: "打开客厅的灯"
艾比: "好的，已经为您打开客厅的灯。"
```

## 🔧 **故障排除**

### **常见问题**

1. **Dify 对话无响应**
   - 检查网络连接
   - 验证 API 密钥是否正确
   - 查看服务器日志

2. **语音识别不准确**
   - 确保环境安静
   - 检查麦克风设置
   - 调整音量和距离

3. **语音合成无声音**
   - 检查扬声器设置
   - 验证音频输出设备
   - 查看 TTS 程序日志

### **日志查看**

```bash
# 查看服务器日志
# 服务器运行在终端中，可以直接查看输出

# 测试单个组件
conda activate aibi
python config/programs/intent/dify/bin/dify_intent.py "测试" --api-key "your-key" --format json
```

## 📊 **性能优化建议**

### **硬件要求**
- **CPU**: 4 核以上推荐
- **内存**: 8GB 以上推荐
- **存储**: 10GB 可用空间（用于模型）
- **网络**: 稳定的互联网连接（Dify API）

### **优化建议**
1. **使用 GPU 加速**（如果可用）
2. **调整模型参数**以平衡速度和质量
3. **优化网络连接**确保 Dify API 响应速度
4. **定期清理日志**和临时文件

## 🎉 **总结**

**当前状态**：
- ✅ **文本对话功能完全可用**
- ✅ **智能家居控制功能正常**
- ✅ **Web 管理界面正常**
- ⚠️ **语音功能需要模型配置**

**下一步**：
1. 配置语音模型以启用完整语音功能
2. 根据实际需求调整 Dify 工作流
3. 优化系统性能和用户体验

**Aibi-Rhasspy 智能语音助手已经可以投入基础使用！** 🎉
