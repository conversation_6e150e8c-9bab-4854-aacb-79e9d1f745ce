# Aibi-Rhasspy 服务端-客户端架构说明

## 🏗️ **架构概述**

Aibi-Rhasspy 采用服务端-客户端分离的架构设计，既保持了 Rhasspy 3.0 的标准化优势，又满足了用户一键启动、自主运行的需求。

### **🔄 架构图**

```
┌─────────────────────────────────────────────────────────────┐
│                    用户语音交互                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                bin/client.py                                │
│              主控语音助手客户端                              │
│                                                             │
│  • 持续监听麦克风                                           │
│  • 检测唤醒词 "Hey, 艾比"                                   │
│  • 调用服务器 API                                           │
│  • 播放回复音频                                             │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP API 调用
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                bin/server.py                                │
│              Rhasspy HTTP API 服务器                        │
│                                                             │
│  • 基于 Rhasspy 3.0 架构                                    │
│  • 提供完整的语音处理 API                                   │
│  • 管理所有语音组件                                         │
└─────────────────────┬───────────────────────────────────────┘
                      │ Wyoming 协议
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  语音处理组件                                │
│                                                             │
│  Wake: hey-aibi     │  ASR: sensevoice                     │
│  Intent: dify       │  TTS: cosyvoice                      │
│  Handle: dify       │  Audio: arecord/aplay                │
└─────────────────────────────────────────────────────────────┘
```

## 🖥️ **服务端 (bin/server.py)**

### **功能职责**
- 启动 Rhasspy 3.0 HTTP API 服务器
- 使用项目配置文件 `config/configuration.yaml`
- 监听端口 13331
- 提供完整的语音处理 API 接口
- 管理所有语音组件的生命周期

### **核心特性**
- ✅ **标准化架构** - 基于 Rhasspy 3.0 和 Wyoming 协议
- ✅ **模块化设计** - 支持各种语音组件的热插拔
- ✅ **API 接口** - 提供 RESTful API 和 WebSocket API
- ✅ **配置管理** - 统一的配置文件管理
- ✅ **错误处理** - 完善的错误处理和日志记录

### **启动方式**
```bash
# 直接启动
conda run -n aibi python bin/server.py

# 指定端口
conda run -n aibi python bin/server.py --port 13331

# 调试模式
conda run -n aibi python bin/server.py --debug
```

### **API 接口**
- `GET /version` - 获取版本信息
- `POST /wake/detect` - 唤醒词检测
- `POST /asr/transcribe` - 语音识别
- `POST /intent/recognize` - 意图识别
- `POST /handle/handle` - 意图处理
- `POST /tts/synthesize` - 语音合成
- `POST /snd/play` - 音频播放
- `POST /pipeline/run` - 完整管道运行

## 🎤 **客户端 (bin/client.py)**

### **功能职责**
- 持续监听麦克风音频输入
- 检测到唤醒词 "Hey, 艾比" 后，调用服务器的完整 pipeline API
- 实现端到端的语音交互流程
- 循环运行，等待下一次语音交互
- 提供清晰的状态反馈和错误处理

### **核心特性**
- ✅ **实时音频处理** - 使用 sounddevice 进行低延迟音频处理
- ✅ **智能唤醒检测** - 持续监听，检测到唤醒词后自动响应
- ✅ **完整交互流程** - 自动完成录音、识别、理解、合成、播放
- ✅ **错误恢复** - 遇到错误后自动恢复到监听状态
- ✅ **用户友好** - 清晰的状态提示和操作指导

### **工作流程**
```python
while True:
    # 1. 持续监听麦克风
    audio_data = listen_microphone()
    
    # 2. 检测唤醒词
    if detect_wake_word(audio_data):
        print("🎯 检测到唤醒词！")
        
        # 3. 录制用户指令
        command_audio = record_command()
        
        # 4. 调用完整的 pipeline API
        response = call_server_pipeline(command_audio)
        
        # 5. 播放回复音频
        play_response_audio(response)
        
        print("🎉 交互完成，继续监听...")
    
    # 6. 短暂休息，避免过度占用 CPU
    time.sleep(0.1)
```

### **启动方式**
```bash
# 连接默认服务器
conda run -n aibi python bin/client.py

# 指定服务器地址
conda run -n aibi python bin/client.py --server http://localhost:13331

# 调试模式
conda run -n aibi python bin/client.py --debug
```

## 🚀 **启动脚本 (scripts/start.sh)**

### **功能职责**
- 一键启动完整的语音助手系统
- 先启动服务器（后台运行）
- 再启动客户端（前台运行）
- 提供完整的生命周期管理

### **核心特性**
- ✅ **一键启动** - 用户只需运行一个命令
- ✅ **自动检查** - 环境、依赖、端口等全面检查
- ✅ **后台管理** - 服务器后台运行，客户端前台交互
- ✅ **优雅停止** - 支持 Ctrl+C 优雅停止所有进程
- ✅ **状态监控** - 实时监控服务器和客户端状态

### **使用方式**
```bash
# 启动语音助手
./scripts/start.sh

# 或者显式指定启动
./scripts/start.sh start

# 停止语音助手
./scripts/start.sh stop

# 检查运行状态
./scripts/start.sh status

# 重启语音助手
./scripts/start.sh restart

# 清理环境
./scripts/start.sh cleanup

# 查看帮助
./scripts/start.sh help
```

### **启动流程**
1. **环境检查** - 检查 Conda 环境、项目文件、端口占用
2. **启动服务器** - 后台启动 Rhasspy HTTP API 服务器
3. **等待就绪** - 等待服务器完全启动并响应
4. **启动客户端** - 前台启动语音助手客户端
5. **用户交互** - 用户可以直接对麦克风说话
6. **优雅停止** - Ctrl+C 时自动停止所有进程

## 🎯 **用户体验**

### **完整的使用流程**

1. **启动系统**
   ```bash
   ./scripts/start.sh
   ```

2. **系统就绪**
   ```
   🎯 语音助手已启动！
   💡 说 'Hey, 艾比' 来唤醒助手
   🛑 按 Ctrl+C 停止程序
   ```

3. **语音交互**
   ```
   用户: "Hey, 艾比"
   系统: 🎯 检测到唤醒词！
   
   用户: "今天天气怎么样？"
   系统: 🗣️ 请说出您的指令...
         🤖 处理语音指令...
         🎵 合成语音...
         🔊 播放回复...
         
   系统: "今天天气晴朗，温度适宜..."
   ```

4. **停止系统**
   ```
   按 Ctrl+C
   系统: 🛑 收到停止信号，正在清理...
         ✅ Rhasspy 服务器已停止
   ```

### **优势特点**

#### **对用户**
- 🎯 **一键启动** - 运行一个脚本就能使用
- 🎤 **自然交互** - 直接对麦克风说话，无需其他操作
- 🔄 **持续运行** - 系统持续监听，随时响应
- 🛑 **简单停止** - Ctrl+C 即可优雅停止

#### **对开发者**
- 🏗️ **标准架构** - 基于 Rhasspy 3.0，符合开源标准
- 🔧 **易于调试** - 服务端-客户端分离，便于独立调试
- 📈 **可扩展性** - 模块化设计，易于添加新功能
- 📊 **监控友好** - 详细的日志和状态监控

## 🔧 **配置说明**

### **服务器配置**
- **端口**: 13331 (可通过 --port 参数修改)
- **配置文件**: `config/configuration.yaml`
- **日志目录**: `logs/`
- **PID 文件**: `logs/aibi_server.pid`

### **客户端配置**
- **服务器地址**: http://localhost:13331 (可通过 --server 参数修改)
- **音频配置**: 16kHz, 单声道, 16位
- **唤醒检测**: 2秒音频窗口
- **指令录制**: 5秒录制时长

### **音频配置**
```python
audio_config = {
    "sample_rate": 16000,      # 采样率
    "channels": 1,             # 声道数
    "dtype": "int16",          # 数据类型
    "blocksize": 1024,         # 块大小
    "wake_duration": 2.0,      # 唤醒检测窗口
    "command_duration": 5.0    # 指令录制时长
}
```

## 🚨 **故障排除**

### **常见问题**

#### **1. 服务器启动失败**
```bash
# 检查端口占用
lsof -i :13331

# 查看服务器日志
tail -f logs/aibi_server_*.log

# 检查 Rhasspy 3.0 安装
ls -la rhasspy3/
```

#### **2. 客户端连接失败**
```bash
# 检查服务器状态
./scripts/start.sh status

# 测试 API 连接
curl http://localhost:13331/version

# 检查网络连接
ping localhost
```

#### **3. 音频设备问题**
```bash
# 检查音频设备
python -c "import sounddevice as sd; print(sd.query_devices())"

# 测试录音
arecord -d 3 test.wav && aplay test.wav

# 检查权限
ls -la /dev/snd/
```

#### **4. 唤醒词检测问题**
```bash
# 检查模型文件
ls -la config/programs/wake/hey-aibi/models/

# 测试唤醒词 API
curl -X POST http://localhost:13331/wake/detect -F "audio=@test.wav"

# 调试模式运行
python bin/client.py --debug
```

## 🎉 **总结**

Aibi-Rhasspy 的服务端-客户端架构成功实现了：

1. **用户友好** - 一键启动，自然交互
2. **技术先进** - 基于 Rhasspy 3.0 标准架构
3. **功能完整** - 端到端的语音交互体验
4. **易于维护** - 模块化设计，清晰的职责分离
5. **扩展性强** - 支持各种语音组件的集成

这个架构既满足了用户对简单易用的需求，又保持了技术架构的先进性和可维护性，是一个理想的语音助手解决方案。
