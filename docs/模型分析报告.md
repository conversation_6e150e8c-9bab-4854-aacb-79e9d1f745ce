# 🔍 Hey, 艾比 唤醒词模型分析报告

## 📋 **模型基本信息**

### **ONNX 模型详情**
- **模型文件**: `models/kws/hey_aibi.onnx`
- **模型版本**: ONNX IR 版本 7
- **生产者**: PyTorch 2.7.1
- **节点数量**: 38 个计算节点
- **模型架构**: TC-ResNet8 (时序卷积残差网络)

### **输入规格**
- **输入名称**: `input`
- **输入形状**: `[batch_size, 1, 100, 40]`
- **数据类型**: `float32`
- **含义解释**:
  - `batch_size`: 批处理大小（动态）
  - `1`: 单声道音频
  - `100`: 时间步长（对应 1 秒音频）
  - `40`: MFCC 特征维度

### **输出规格**
- **输出名称**: `output`
- **输出形状**: `[batch_size, 1]`
- **数据类型**: `float32`
- **含义**: 唤醒词检测的原始分数（需要 Sigmoid 激活）

## 🎯 **模型要求分析**

### **音频预处理要求**
根据 `model_info.txt` 和模型结构分析：

```yaml
音频参数:
  采样率: 16000 Hz
  声道数: 1 (单声道)
  音频长度: 1 秒 (16000 样本)

MFCC 参数:
  特征维度: 40
  窗长: 25ms (400 样本)
  帧移: 10ms (160 样本)
  时间帧数: 100 帧
  频率范围: 0-8000 Hz
```

### **推理流程**
1. **音频采集**: 16kHz 单声道 PCM 音频
2. **特征提取**: 提取 40 维 MFCC 特征
3. **窗口处理**: 滑动窗口，每次处理 1 秒音频
4. **模型推理**: ONNX Runtime 推理
5. **后处理**: Sigmoid 激活 + 阈值判断

### **阈值设置**
- **推荐阈值**: 0.85 (根据 model_info.txt)
- **输出处理**: `sigmoid(raw_output) > threshold`

## 🔧 **技术实现要点**

### **1. MFCC 特征提取**
```python
import librosa
import numpy as np

def extract_mfcc(audio, sr=16000):
    """提取符合模型要求的 MFCC 特征"""
    mfcc = librosa.feature.mfcc(
        y=audio,
        sr=sr,
        n_mfcc=40,
        n_fft=512,      # 对应 25ms 窗长
        hop_length=160, # 对应 10ms 帧移
        n_mels=40
    )
    return mfcc.T  # 转置为 [time, features]
```

### **2. 滑动窗口处理**
```python
class SlidingWindow:
    def __init__(self, window_size=16000):  # 1 秒
        self.window_size = window_size
        self.buffer = np.array([], dtype=np.float32)
    
    def add_audio(self, audio_chunk):
        """添加音频块并返回完整窗口"""
        self.buffer = np.concatenate([self.buffer, audio_chunk])
        if len(self.buffer) >= self.window_size:
            window = self.buffer[-self.window_size:]
            return window
        return None
```

### **3. ONNX 推理**
```python
import onnxruntime as ort

class WakeWordDetector:
    def __init__(self, model_path, threshold=0.85):
        self.session = ort.InferenceSession(model_path)
        self.threshold = threshold
    
    def detect(self, mfcc_features):
        """检测唤醒词"""
        # 调整形状为 [1, 1, 100, 40]
        input_data = mfcc_features.reshape(1, 1, 100, 40)
        
        # 推理
        output = self.session.run(None, {'input': input_data})
        raw_score = output[0][0][0]
        
        # Sigmoid 激活
        confidence = 1.0 / (1.0 + np.exp(-raw_score))
        
        return confidence > self.threshold, confidence
```

## ⚡ **性能特点**

### **模型优势**
- ✅ **轻量级**: 38 个节点，推理速度快
- ✅ **低延迟**: 支持实时处理
- ✅ **高精度**: TC-ResNet8 架构，准确率高
- ✅ **低功耗**: 适合边缘设备部署

### **资源需求**
- **内存占用**: < 50MB
- **推理时间**: < 10ms (CPU)
- **音频缓冲**: 1 秒滑动窗口

## 🎯 **集成策略**

### **Rhasspy 3.0 集成要点**
1. **程序结构**: 遵循 `programs/wake/hey-aibi/` 标准
2. **输入接口**: 接收 16kHz 原始 PCM 音频流
3. **输出格式**: 输出唤醒词名称到标准输出
4. **配置参数**: 支持阈值、模型路径等配置

### **实时处理流程**
```
音频流 → 缓冲区 → MFCC提取 → 模型推理 → 阈值判断 → 输出结果
  ↓         ↓        ↓        ↓        ↓        ↓
16kHz    滑动窗口   40维特征  ONNX推理  Sigmoid  "hey_aibi"
```

## 📝 **下一步实现计划**

### **任务 1.2**: 实现 MFCC 特征提取器
- 基于 librosa 实现标准 MFCC 提取
- 确保参数与训练时一致
- 优化实时处理性能

### **任务 1.3**: 开发 ONNX 唤醒词检测器
- 实现滑动窗口音频缓冲
- 集成 ONNX Runtime 推理
- 添加置信度计算和阈值判断

### **任务 1.4**: 集成到 Rhasspy 3.0
- 创建标准程序结构
- 实现命令行接口
- 添加配置文件支持

### **任务 1.5**: 配置和测试
- 更新 configuration.yaml
- 编写测试脚本
- 验证端到端功能

---

**✅ 任务 1.1 完成**: 模型结构分析完毕，为后续实现提供了清晰的技术规格和实现路径。
