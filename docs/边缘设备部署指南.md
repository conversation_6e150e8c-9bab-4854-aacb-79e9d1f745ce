# 🤖 Aibi 语音助手边缘设备部署指南

## 🎯 **概述**

本指南专门为**边缘设备**（树莓派、边缘盒子等）设计，提供**开机自启动**的完整语音助手解决方案。

### **✅ 您的需求 vs 我们的方案**

| 您的需求 | 我们的解决方案 |
|----------|----------------|
| 🔌 **开机自启动** | systemd 服务，开机自动启动 |
| 🎤 **等待唤醒** | 持续监听 "Hey, 艾比" 唤醒词 |
| 🔄 **完整管道** | 唤醒→录音→ASR→意图→TTS→播放 |
| 🚫 **无需 Web** | 纯命令行，无 Web 界面依赖 |
| 🏠 **边缘设备** | 优化资源使用，适配树莓派等 |

## 🚀 **一键部署**

### **方法 1：快速部署（推荐）**

```bash
# 在项目根目录执行
cd /home/<USER>/project/Aibi-Rhasspy

# 一键部署
./scripts/quick_deploy_edge.sh
```

### **方法 2：分步部署**

```bash
# 1. 检查环境
./scripts/quick_deploy_edge.sh --check

# 2. 测试功能
./scripts/quick_deploy_edge.sh --test

# 3. 完整安装
./scripts/install_edge_service.sh
```

## 🎯 **部署后的工作流程**

### **自动启动流程**
```
开机 → systemd 启动服务 → 加载模型 → 等待唤醒词
  ↓
"Hey, 艾比" → 开始录音 → ASR 识别 → Dify 理解 → TTS 合成 → 播放回复
  ↓
回到等待状态 → 继续监听唤醒词...
```

### **用户交互流程**
1. **说出唤醒词**: "Hey, 艾比"
2. **等待提示**: 系统检测到唤醒词
3. **说出指令**: "今天天气怎么样？"
4. **听取回复**: AI 助手语音回复
5. **继续对话**: 重复步骤 1-4

## 🔧 **服务管理**

### **基本命令**
```bash
# 查看服务状态
sudo systemctl status aibi-assistant

# 启动服务
sudo systemctl start aibi-assistant

# 停止服务
sudo systemctl stop aibi-assistant

# 重启服务
sudo systemctl restart aibi-assistant

# 查看实时日志
sudo journalctl -u aibi-assistant -f

# 查看最近日志
sudo journalctl -u aibi-assistant -n 50
```

### **开机自启动管理**
```bash
# 启用开机自启动（默认已启用）
sudo systemctl enable aibi-assistant

# 禁用开机自启动
sudo systemctl disable aibi-assistant

# 检查是否已启用
sudo systemctl is-enabled aibi-assistant
```

## 📊 **系统监控**

### **实时状态监控**
```bash
# 查看详细状态
sudo systemctl status aibi-assistant -l

# 查看资源使用
top -p $(pgrep -f aibi_voice_assistant)

# 查看内存使用
ps aux | grep aibi_voice_assistant
```

### **日志分析**
```bash
# 查看启动日志
sudo journalctl -u aibi-assistant --since "10 minutes ago"

# 查看错误日志
sudo journalctl -u aibi-assistant -p err

# 查看应用日志
tail -f /home/<USER>/project/Aibi-Rhasspy/logs/aibi_assistant_*.log
```

## 🛠️ **配置优化**

### **边缘设备配置文件**
配置文件位置: `config/edge_config.yaml`

#### **音频配置优化**
```yaml
audio:
  sample_rate: 16000      # 降低采样率节省资源
  channels: 1             # 单声道
  chunk_size: 1024        # 适中的块大小
  record_seconds: 5       # 录音时长
```

#### **性能优化配置**
```yaml
system:
  memory_limit: "2G"      # 内存限制
  cpu_limit: 80           # CPU 使用限制
  timeouts:
    wake_detection: 10    # 唤醒检测超时
    asr: 30              # 语音识别超时
    tts: 120             # 语音合成超时
```

#### **边缘设备特殊配置**
```yaml
edge:
  device_type: "edge_box"
  power_save: true        # 节能模式
  monitor_resources: true # 资源监控
  watchdog_enabled: true  # 看门狗
```

## 🔧 **故障排除**

### **常见问题**

#### **1. 服务启动失败**
```bash
# 查看详细错误
sudo journalctl -u aibi-assistant -n 20

# 手动运行调试
conda activate aibi
python /home/<USER>/project/Aibi-Rhasspy/scripts/aibi_voice_assistant.py
```

#### **2. 音频设备问题**
```bash
# 检查录音设备
arecord -l

# 检查播放设备
aplay -l

# 测试录音
arecord -d 3 test.wav

# 测试播放
aplay test.wav
```

#### **3. 模型加载失败**
```bash
# 检查模型文件
ls -la config/programs/*/models/

# 检查 conda 环境
conda env list
conda activate aibi
python -c "import torch; print(torch.__version__)"
```

#### **4. 网络连接问题**
```bash
# 检查 Dify API 连接
curl -X POST "http://***********/..." --timeout 5

# 检查网络连接
ping *******
```

### **性能优化建议**

#### **内存优化**
- 使用较小的模型批处理大小
- 及时清理临时文件
- 监控内存使用情况

#### **CPU 优化**
- 限制 CPU 使用率
- 使用节能模式
- 优化模型推理参数

#### **存储优化**
- 定期清理日志文件
- 压缩模型文件
- 使用高速 SD 卡

## 📱 **边缘设备适配**

### **树莓派 4B 配置**
```bash
# 推荐配置
- RAM: 4GB 或更高
- SD 卡: Class 10, 32GB+
- 音频: USB 麦克风 + 扬声器
- 网络: 有线连接（推荐）
```

### **工控机配置**
```bash
# 推荐配置
- RAM: 8GB 或更高
- 存储: SSD 128GB+
- 音频: 集成或 USB 音频设备
- 网络: 有线连接
```

### **边缘盒子配置**
```bash
# 推荐配置
- ARM64 或 x86_64 架构
- RAM: 4GB 或更高
- 存储: eMMC 64GB+
- 音频: 集成音频芯片
```

## 🎯 **使用场景**

### **智能家居控制中心**
- 语音控制家电设备
- 场景模式切换
- 环境监控和报告
- 安防系统集成

### **边缘 AI 助手**
- 离线语音交互
- 本地数据处理
- 隐私保护
- 低延迟响应

### **工业物联网**
- 设备状态查询
- 故障报告
- 操作指令执行
- 数据采集控制

## 🔄 **更新和维护**

### **系统更新**
```bash
# 停止服务
sudo systemctl stop aibi-assistant

# 更新代码
cd /home/<USER>/project/Aibi-Rhasspy
git pull

# 重新部署
./scripts/quick_deploy_edge.sh

# 启动服务
sudo systemctl start aibi-assistant
```

### **模型更新**
```bash
# 备份现有模型
cp -r config/programs/*/models/ backup/

# 替换新模型
# ... 复制新模型文件 ...

# 重启服务
sudo systemctl restart aibi-assistant
```

### **配置更新**
```bash
# 编辑配置
nano config/edge_config.yaml

# 重启服务使配置生效
sudo systemctl restart aibi-assistant
```

## 📋 **检查清单**

### **部署前检查**
- [ ] conda 环境 'aibi' 已创建
- [ ] 所有模型文件已下载
- [ ] 音频设备已连接并测试
- [ ] 网络连接正常
- [ ] 存储空间充足（>10GB）

### **部署后验证**
- [ ] 服务状态正常
- [ ] 日志无错误信息
- [ ] 唤醒词检测正常
- [ ] 语音识别功能正常
- [ ] 语音合成功能正常
- [ ] 完整交互流程正常

### **长期维护**
- [ ] 定期检查服务状态
- [ ] 监控系统资源使用
- [ ] 清理日志文件
- [ ] 更新模型和代码
- [ ] 备份重要配置

---

## 🎉 **总结**

### **✅ 您现在拥有了：**

1. **🔌 开机自启动语音助手** - 无需手动启动
2. **🎤 完整语音交互管道** - 从唤醒到回复的全流程
3. **🏠 边缘设备优化** - 适配资源受限环境
4. **🔧 完善的管理工具** - 服务管理、监控、故障排除
5. **📚 详细的文档支持** - 部署、配置、维护指南

### **🎯 这正是您需要的边缘设备解决方案！**

**不需要 Web 界面，不需要手动启动，开机即用的智能语音助手！**

现在您可以将设备部署到任何地方，说出 "Hey, 艾比" 就能开始语音交互了！ 🎊
