# 🐍 环境配置说明

## 🤔 **为什么选择 Conda 环境？**

经过重新考虑，我们决定使用 **Conda 环境** 而不是 Python venv，原因如下：

### **Conda 环境的优势**

1. **🔧 更强大的依赖管理**
   - 自动解决复杂的依赖冲突
   - 支持系统级库（如 CUDA、音频库）
   - 更好的包版本兼容性

2. **🎯 统一的环境管理**
   - 所有组件共享同一个环境
   - 避免多个虚拟环境的管理复杂性
   - 与用户现有工作流一致

3. **⚡ 更好的性能**
   - 预编译的二进制包
   - 优化的数学库（MKL、OpenBLAS）
   - 更快的包安装速度

4. **🌍 跨平台兼容性**
   - 统一的包管理体验
   - 更好的 Windows/macOS 支持
   - 自动处理平台差异

## 🚀 **环境配置方案**

### **方案对比**

| 特性 | 原方案 (Python venv) | 新方案 (Conda) |
|------|---------------------|----------------|
| **环境管理** | 每个程序独立 `.venv` | 统一 `aibi` 环境 |
| **依赖安装** | `pip install` | `conda install` + `pip install` |
| **系统库** | 需手动处理 | 自动管理 |
| **磁盘占用** | 多个小环境 | 一个大环境 |
| **维护复杂度** | 较高 | 较低 |
| **用户体验** | 需要理解多环境 | 简单统一 |

### **新的目录结构**

```
Aibi-Rhasspy/
├── config/
│   ├── programs/
│   │   ├── wake/hey-aibi/
│   │   │   ├── bin/hey_aibi_raw_text.py
│   │   │   ├── script/setup              # 使用 conda 环境
│   │   │   └── requirements.txt
│   │   └── asr/sensevoice/
│   │       ├── bin/sensevoice_wav2text.py
│   │       ├── script/setup              # 使用 conda 环境
│   │       └── requirements.txt
│   └── configuration.yaml                # 使用 conda run
├── environment.yml                       # 统一环境配置
└── scripts/
    └── setup_environment.sh              # 环境初始化脚本
```

## 📦 **统一环境配置**

### **创建环境配置文件**

```yaml
# environment.yml
name: aibi
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  - python=3.10
  - numpy>=1.21.0
  - scipy>=1.7.0
  - librosa>=0.10.0
  - soundfile>=0.12.0
  - pytorch>=1.13.0
  - torchaudio>=0.13.0
  - pip
  - pip:
    - funasr>=1.0.0
    - modelscope>=1.9.0
    - jieba>=0.42.0
    - pydub>=0.25.0
    - onnxruntime>=1.15.0
```

### **环境初始化脚本**

```bash
#!/bin/bash
# scripts/setup_environment.sh

# 创建或更新 conda 环境
conda env create -f environment.yml

# 或更新现有环境
# conda env update -f environment.yml

echo "✅ 环境配置完成！"
echo "🚀 激活环境: conda activate aibi"
```

## 🔧 **配置更新**

### **Rhasspy 3.0 配置**

```yaml
# config/configuration.yaml
programs:
  wake:
    hey-aibi:
      command: |
        conda run -n aibi python bin/hey_aibi_raw_text.py --model "${model}" --threshold ${threshold}
      
  asr:
    sensevoice:
      command: |
        conda run -n aibi python bin/sensevoice_wav2text.py "${wav_file}" --language "${language}"
```

### **程序安装脚本**

```bash
# 各程序的 script/setup
#!/bin/bash

# 检查 conda 环境
if [ -z "$CONDA_DEFAULT_ENV" ]; then
    echo "❌ 请先激活 conda 环境: conda activate aibi"
    exit 1
fi

# 安装依赖到当前环境
pip install -r requirements.txt

echo "✅ 安装完成！"
```

## 🎯 **使用方法**

### **初始化环境**

```bash
# 1. 创建环境
conda env create -f environment.yml

# 2. 激活环境
conda activate aibi

# 3. 安装各组件
cd config/programs/wake/hey-aibi && ./script/setup
cd config/programs/asr/sensevoice && ./script/setup
```

### **日常使用**

```bash
# 激活环境
conda activate aibi

# 运行唤醒词测试
./scripts/continuous_wake_test.py

# 运行 ASR 测试
python config/programs/asr/sensevoice/bin/sensevoice_wav2text.py audio.wav

# 运行完整管道
# Rhasspy 3.0 会自动使用 conda run 命令
```

## 💡 **迁移指南**

### **从 venv 迁移到 conda**

1. **备份现有环境**
   ```bash
   # 导出已安装的包
   pip freeze > backup_requirements.txt
   ```

2. **清理旧环境**
   ```bash
   # 删除 .venv 目录
   find . -name ".venv" -type d -exec rm -rf {} +
   ```

3. **创建新环境**
   ```bash
   # 使用统一的 conda 环境
   conda activate aibi
   pip install -r backup_requirements.txt
   ```

## 🎉 **优势总结**

采用 Conda 环境后，您将获得：

1. **🎯 简化的环境管理**：只需管理一个 `aibi` 环境
2. **⚡ 更好的性能**：优化的数学库和预编译包
3. **🔧 更强的兼容性**：自动处理系统依赖
4. **📦 统一的工作流**：与您现有的使用习惯一致
5. **🚀 更快的部署**：减少环境配置复杂度

**这个改进让整个系统更加简洁、高效和易于维护！** 👍
